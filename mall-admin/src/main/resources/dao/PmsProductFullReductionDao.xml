<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.PmsProductFullReductionDao">
    <insert id="insertList">
        INSERT INTO pms_product_full_reduction (product_id, full_price, reduce_price) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.productId,jdbcType=BIGINT},
            #{item.fullPrice,jdbcType=DECIMAL},
            #{item.reducePrice,jdbcType=DECIMAL})
        </foreach>
    </insert>
</mapper>