<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.PmsMemberPriceDao">
    <!--批量新增回写主键支持-->
    <insert id="insertList">
        INSERT INTO pms_member_price (product_id, member_level_id, member_price,member_level_name) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.productId,jdbcType=BIGINT},
            #{item.memberLevelId,jdbcType=BIGINT},
            #{item.memberPrice,jdbcType=DECIMAL},
            #{item.memberLevelName,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>