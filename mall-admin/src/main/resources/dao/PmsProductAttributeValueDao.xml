<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.PmsProductAttributeValueDao">
    <insert id="insertList">
        insert into pms_product_attribute_value (product_id,product_attribute_id,value) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productId,jdbcType=BIGINT},
            #{item.productAttributeId,jdbcType=BIGINT},
            #{item.value,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>