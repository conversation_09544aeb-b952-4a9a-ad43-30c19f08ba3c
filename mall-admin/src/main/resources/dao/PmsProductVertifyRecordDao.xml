<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.PmsProductVertifyRecordDao">
    <!--批量新增回写主键支持-->
    <insert id="insertList">
        insert into pms_product_vertify_record (product_id, create_time, vertify_man,status, detail) values
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.productId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.vertifyMan,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER},
            #{item.detail,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>