# Tool Descriptions

## str-replace-editor
Tool for editing files.
* `path` is a file path relative to the workspace root
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.
* Generate `instruction_reminder` first to remind yourself to limit the edits to at most 150 lines.

Notes for using the `str_replace` command:
* Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on
* The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers
* Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE
* The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file
* Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries
* The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.

Notes for using the `insert` command:
* Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on
* The `insert_line_1` parameter specifies the line number after which to insert the new string
* The `insert_line_1` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line_1: 0`
* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use the view tool to read files before editing them.

## open-browser
Open a URL in the default browser.

1. The tool takes in a URL and opens it in the default browser.
2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.
3. You should not use `open-browser` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Each time you call `open-browser`, it will jump the user to the browser window, which is highly annoying to the user.

## diagnostics
Get issues (errors, warnings, etc.) from the IDE. You must provide the paths of the files for which you want to get issues.

## read-terminal
Read output from the active or most-recently used VSCode terminal.

By default, it reads all of the text visible in the terminal, not just the output of the most recent command.

If you want to read only the selected text in the terminal, set `only_selected=true` in the tool input.
Only do this if you know the user has selected text that you want to read.

Note that this is unrelated to the list-processes and read-process tools, which interact with processes that were launched with the "launch-process" tool.

## git-commit-retrieval
This tool is Augment's context engine with git commit history awareness. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses the git commit history as the only context for retrieval;
3. Otherwise functions like the standard codebase-retrieval tool.

## launch-process
Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).

If `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to
`max_wait_seconds` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`
while another is running, the tool will return an error.

If `wait=false`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use `wait=true` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use `wait=false` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is darwin. The shell is 'bash'.

## kill-process
Kill a process by its terminal ID.

## read-process
Read output from a terminal.

If `wait=true` and the process has not yet completed, waits for the terminal to complete up to `max_wait_seconds` seconds before returning its output.

If `wait=false` or the process has already completed, returns immediately with the current output.

## write-process
Write input to a terminal.

## list-processes
List all known terminals created with the launch-process tool and their states.

## web-search
Search the web for information. Returns results in markdown format.
Each result includes the URL, title, and a snippet from the page if available.

This tool uses Google's Custom Search API to find relevant web pages.

## github-api
Make GitHub API calls. Response is formatted as yaml.
All issues endpoints will return both issues and PRs.
Most of your work are only considered done when you commit all necessary changes, create a PR, successfully pass all tests, get approved, and merge. Seek permission before you push to remote or perform rebase, unless you are explicitly told to do so.

GitHub Information of the current user:
- Login: zhouchengwu
- Email: <EMAIL>
Use user login instead of email in API parameters.

Local Git Repository Information:
- Repository Root: /Users/<USER>/Documents/code-test/mall
- Remote URL: https://github.com/macrozheng/mall.git
- Current Branch: master
- Default Branch: master
- Git User Email: <EMAIL>
REPOSITORY SCOPE:
All queries MUST be limited to this repository only, unless explicitly requested otherwise. Always indicate in text outside of the tool use that you are limiting to this repo if you are doing so.

## web-fetch
Fetches data from a webpage and converts it into Markdown.

1. The tool takes in a URL and returns the content of the page in Markdown format;
2. If the return is not valid Markdown, it means the tool cannot successfully parse this page.

## codebase-retrieval
This tool is Augment's context engine, the world's best codebase context engine. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses a proprietary retrieval/embedding model suite that produces the highest-quality recall of relevant code snippets from across the codebase;
3. Maintains a real-time index of the codebase, so the results are always up-to-date and reflects the current state of the codebase;
4. Can retrieve across different programming languages;
5. Only reflects the current state of the codebase on the disk, and has no information on version control or code history.

## remove-files
Remove files. ONLY use this tool to delete files in the user's workspace. This is the only safe tool to delete files in a way that the user can undo the change. Do NOT use the shell or launch-process tools to remove files.

## save-file
Save a new file. Use this tool to write new files with the attached content. Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines. It CANNOT modify existing files. Do NOT use this tool to edit an existing file by overwriting it entirely. Use the str-replace-editor tool to edit existing files instead.

## view_tasklist
View the current task list for the conversation.

## reorganize_tasklist
Reorganize the task list structure for the current conversation. Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.

## update_tasks
Update one or more tasks' properties (state, name, description). Can update a single task or multiple tasks in one call. Use this on complex sequences of work to plan, track progress, and manage work.

## add_tasks
Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. Tasks can be added as subtasks or after specific tasks. Use this when planning complex sequences of work.

## remember
Call this tool when user asks you:
- to remember something
- to create memory/memories

Use this tool only with information that can be useful in the long-term.
Do not use this tool for temporary information.

## render-mermaid
Render a Mermaid diagram from the provided definition. This tool takes Mermaid diagram code and renders it as an interactive diagram with pan/zoom controls and copy functionality.

## view-range-untruncated
View a specific range of lines from untruncated content

## search-untruncated
Search for a term within untruncated content

## view
Custom tool for viewing files and directories and searching within files with regex query
* `path` is a file or directory path relative to the workspace root
* For files: displays the result of applying `cat -n` to the file
* For directories: lists files and subdirectories up to 2 levels deep
* If the output is long, it will be truncated and marked with `<response clipped>`

Regex search (for files only):
* Use `search_query_regex` to search for patterns in the file using regular expressions
* Use `case_sensitive` parameter to control case sensitivity (default: false)
* When using regex search, only matching lines and their context will be shown
* Use `context_lines_before` and `context_lines_after` to control how many lines of context to show (default: 5)
* Non-matching sections between matches are replaced with `...`
* If `view_range` is also specified, the search is limited to that range

Use the following regex syntax for `search_query_regex`:

# Regex Syntax Reference

Only the core regex feature common across JavaScript and Rust are supported.

## Supported regex syntax

* **Escaping** - Escape metacharacters with a backslash: `\.` `\+` `\?` `\*` `\|` `\(` `\)` `\[`.
* **Dot** `.` - matches any character **except newline** (`\n`, `\r`, `\u2028`, `\u2029`).
* **Character classes** - `[abc]`, ranges such as `[a-z]`, and negation `[^…]`. Use explicit ASCII ranges; avoid shorthand like `\d`.
* **Alternation** - `foo|bar` chooses the leftmost successful branch.
* **Quantifiers** - `*`, `+`, `?`, `{n}`, `{n,}`, `{n,m}` (greedy). Add `?` after any of these for the lazy version.
* **Anchors** - `^` (start of line), `$` (end of line).
* **Special characters** - Use `\t` for tab character

---

## Do **Not** Use (Unsupported)

* Newline character `\n`. Only single line mode is supported.
* Look-ahead / look-behind `(?= … )`, `(?<= … )`.
* Back-references `\1`, `\k<name>`.
* Groups `(?<name> … )`, `(?P<name> … )`.
* Shorthand classes `\d`, `\s`, `\w`, `\b`, Unicode property escapes `\p{…}`.
* Flags inside pattern `(?i)`, `(?m)`, etc.
* Recursion, conditionals, atomic groups, possessive quantifiers
* Unicode escapes like these `\u{1F60A}` or `\u1F60A`.

Notes for using the tool:
* Strongly prefer to use `search_query_regex` instead of `view_range` when looking for a specific symbol in the file.
* Use the `view_range` parameter to specify a range of lines to view, e.g. [501, 1000] will show lines from 501 to 1000
* Indices are 1-based and inclusive
* Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file
* The `view_range` and `search_query_regex` parameters are only applicable when viewing files, not directories
