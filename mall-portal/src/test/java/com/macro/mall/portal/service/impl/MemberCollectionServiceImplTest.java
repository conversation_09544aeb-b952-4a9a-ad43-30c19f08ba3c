package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.domain.MemberProductCollection;
import com.macro.mall.portal.repository.MemberProductCollectionRepository;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MemberCollectionServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class MemberCollectionServiceImplTest {

    @Mock
    private PmsProductMapper productMapper;
    @Mock
    private MemberProductCollectionRepository productCollectionRepository;
    @Mock
    private UmsMemberService memberService;
    @InjectMocks
    private MemberCollectionServiceImpl memberCollectionService;

    private UmsMember mockMember;
    private PmsProduct mockProduct;

    @BeforeEach
    void setUp() {
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setNickname("testUser");
        mockMember.setIcon("icon.jpg");

        mockProduct = new PmsProduct();
        mockProduct.setId(1L);
        mockProduct.setName("测试商品");
        mockProduct.setPrice(new BigDecimal("100.00"));
        mockProduct.setDeleteStatus(0);
    }

    @Test
    void testAdd_Success() {
        ReflectionTestUtils.setField(memberCollectionService, "sqlEnable", true);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(productCollectionRepository.findByMemberIdAndProductId(1L, 1L)).thenReturn(null);
        when(productMapper.selectByPrimaryKey(1L)).thenReturn(mockProduct);

        MemberProductCollection collection = new MemberProductCollection();
        collection.setProductId(1L);

        int result = memberCollectionService.add(collection);
        assertEquals(1, result);
    }

    @Test
    void testAdd_ProductIdNull() {
        MemberProductCollection collection = new MemberProductCollection();
        collection.setProductId(null);
        assertEquals(0, memberCollectionService.add(collection));
    }

    @Test
    void testDelete_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(productCollectionRepository.deleteByMemberIdAndProductId(1L, 1L)).thenReturn(1);
        assertEquals(1, memberCollectionService.delete(1L));
    }

    @Test
    void testList_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(productCollectionRepository.findByMemberId(eq(1L), any()))
                .thenReturn(new PageImpl<>(new ArrayList<>()));
        
        Page<MemberProductCollection> result = memberCollectionService.list(1, 10);
        assertNotNull(result);
    }

    @Test
    void testClear_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        memberCollectionService.clear();
        verify(productCollectionRepository).deleteAllByMemberId(1L);
    }
}