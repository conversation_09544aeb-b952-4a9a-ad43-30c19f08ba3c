package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.domain.MemberReadHistory;
import com.macro.mall.portal.repository.MemberReadHistoryRepository;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MemberReadHistoryServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class MemberReadHistoryServiceImplTest {

    @Mock
    private PmsProductMapper productMapper;
    @Mock
    private MemberReadHistoryRepository memberReadHistoryRepository;
    @Mock
    private UmsMemberService memberService;
    @InjectMocks
    private MemberReadHistoryServiceImpl memberReadHistoryService;

    private UmsMember mockMember;
    private PmsProduct mockProduct;

    @BeforeEach
    void setUp() {
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setNickname("testUser");

        mockProduct = new PmsProduct();
        mockProduct.setId(1L);
        mockProduct.setName("测试商品");
        mockProduct.setPrice(new BigDecimal("100.00"));
        mockProduct.setDeleteStatus(0);
    }

    @Test
    void testCreate_Success() {
        ReflectionTestUtils.setField(memberReadHistoryService, "sqlEnable", true);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(productMapper.selectByPrimaryKey(1L)).thenReturn(mockProduct);

        MemberReadHistory history = new MemberReadHistory();
        history.setProductId(1L);

        int result = memberReadHistoryService.create(history);
        assertEquals(1, result);
        verify(memberReadHistoryRepository).save(any(MemberReadHistory.class));
    }

    @Test
    void testCreate_ProductIdNull() {
        MemberReadHistory history = new MemberReadHistory();
        history.setProductId(null);
        assertEquals(0, memberReadHistoryService.create(history));
    }

    @Test
    void testDelete_Success() {
        assertEquals(3, memberReadHistoryService.delete(Arrays.asList("id1", "id2", "id3")));
        verify(memberReadHistoryRepository).deleteAll(any());
    }

    @Test
    void testList_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberReadHistoryRepository.findByMemberIdOrderByCreateTimeDesc(eq(1L), any()))
                .thenReturn(new PageImpl<>(new ArrayList<>()));
        
        assertNotNull(memberReadHistoryService.list(1, 10));
    }

    @Test
    void testClear_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        memberReadHistoryService.clear();
        verify(memberReadHistoryRepository).deleteAllByMemberId(1L);
    }
}