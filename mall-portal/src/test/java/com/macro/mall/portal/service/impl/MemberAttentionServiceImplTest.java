package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.PmsBrandMapper;
import com.macro.mall.model.PmsBrand;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.domain.MemberBrandAttention;
import com.macro.mall.portal.repository.MemberBrandAttentionRepository;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MemberAttentionServiceImpl单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MemberAttentionServiceImplTest {

    @Mock
    private PmsBrandMapper brandMapper;

    @Mock
    private MemberBrandAttentionRepository memberBrandAttentionRepository;

    @Mock
    private UmsMemberService memberService;

    @InjectMocks
    private MemberAttentionServiceImpl memberAttentionService;

    private UmsMember mockMember;
    private MemberBrandAttention mockAttention;
    private PmsBrand mockBrand;

    @BeforeEach
    void setUp() {
        // Mock member
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setNickname("testUser");
        mockMember.setIcon("test_icon.jpg");

        // Mock attention
        mockAttention = new MemberBrandAttention();
        mockAttention.setMemberId(1L);
        mockAttention.setBrandId(1L);
        mockAttention.setBrandName("测试品牌");
        mockAttention.setBrandLogo("brand_logo.jpg");
        mockAttention.setMemberNickname("testUser");
        mockAttention.setMemberIcon("test_icon.jpg");
        mockAttention.setCreateTime(new Date());

        // Mock brand
        mockBrand = new PmsBrand();
        mockBrand.setId(1L);
        mockBrand.setName("测试品牌");
        mockBrand.setLogo("brand_logo.jpg");
    }

    @Test
    void testAdd_Success_WithSqlEnabled() {
        // Given
        ReflectionTestUtils.setField(memberAttentionService, "sqlEnable", true);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberIdAndBrandId(1L, 1L)).thenReturn(null);
        when(brandMapper.selectByPrimaryKey(1L)).thenReturn(mockBrand);
        when(memberBrandAttentionRepository.save(any(MemberBrandAttention.class))).thenReturn(mockAttention);

        MemberBrandAttention attention = new MemberBrandAttention();
        attention.setBrandId(1L);

        // When
        int result = memberAttentionService.add(attention);

        // Then
        assertEquals(1, result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberIdAndBrandId(1L, 1L);
        verify(brandMapper).selectByPrimaryKey(1L);
        verify(memberBrandAttentionRepository).save(any(MemberBrandAttention.class));
    }

    @Test
    void testAdd_Success_WithSqlDisabled() {
        // Given
        ReflectionTestUtils.setField(memberAttentionService, "sqlEnable", false);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberIdAndBrandId(1L, 1L)).thenReturn(null);
        when(memberBrandAttentionRepository.save(any(MemberBrandAttention.class))).thenReturn(mockAttention);

        MemberBrandAttention attention = new MemberBrandAttention();
        attention.setBrandId(1L);

        // When
        int result = memberAttentionService.add(attention);

        // Then
        assertEquals(1, result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberIdAndBrandId(1L, 1L);
        verify(brandMapper, never()).selectByPrimaryKey(anyLong());
        verify(memberBrandAttentionRepository).save(any(MemberBrandAttention.class));
    }

    @Test
    void testAdd_BrandIdNull() {
        // Given
        MemberBrandAttention attention = new MemberBrandAttention();
        attention.setBrandId(null);

        // When
        int result = memberAttentionService.add(attention);

        // Then
        assertEquals(0, result);
        verify(memberService, never()).getCurrentMember();
        verify(memberBrandAttentionRepository, never()).save(any());
    }

    @Test
    void testAdd_AttentionAlreadyExists() {
        // Given
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberIdAndBrandId(1L, 1L)).thenReturn(mockAttention);

        MemberBrandAttention attention = new MemberBrandAttention();
        attention.setBrandId(1L);

        // When
        int result = memberAttentionService.add(attention);

        // Then
        assertEquals(0, result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberIdAndBrandId(1L, 1L);
        verify(memberBrandAttentionRepository, never()).save(any());
    }

    @Test
    void testAdd_BrandNotFound() {
        // Given
        ReflectionTestUtils.setField(memberAttentionService, "sqlEnable", true);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberIdAndBrandId(1L, 1L)).thenReturn(null);
        when(brandMapper.selectByPrimaryKey(1L)).thenReturn(null);

        MemberBrandAttention attention = new MemberBrandAttention();
        attention.setBrandId(1L);

        // When
        int result = memberAttentionService.add(attention);

        // Then
        assertEquals(0, result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberIdAndBrandId(1L, 1L);
        verify(brandMapper).selectByPrimaryKey(1L);
        verify(memberBrandAttentionRepository, never()).save(any());
    }

    @Test
    void testDelete_Success() {
        // Given
        Long brandId = 1L;
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.deleteByMemberIdAndBrandId(1L, brandId)).thenReturn(1);

        // When
        int result = memberAttentionService.delete(brandId);

        // Then
        assertEquals(1, result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).deleteByMemberIdAndBrandId(1L, brandId);
    }

    @Test
    void testList_Success() {
        // Given
        Integer pageNum = 1;
        Integer pageSize = 10;
        List<MemberBrandAttention> attentionList = new ArrayList<>();
        attentionList.add(mockAttention);
        Page<MemberBrandAttention> mockPage = new PageImpl<>(attentionList);
        
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberId(eq(1L), any(Pageable.class)))
                .thenReturn(mockPage);

        // When
        Page<MemberBrandAttention> result = memberAttentionService.list(pageNum, pageSize);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(mockAttention, result.getContent().get(0));
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberId(eq(1L), any(Pageable.class));
    }

    @Test
    void testList_VerifyPageable() {
        // Given
        Integer pageNum = 2;
        Integer pageSize = 5;
        List<MemberBrandAttention> attentionList = new ArrayList<>();
        Page<MemberBrandAttention> mockPage = new PageImpl<>(attentionList);
        
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberId(eq(1L), any(Pageable.class)))
                .thenReturn(mockPage);

        // When
        memberAttentionService.list(pageNum, pageSize);

        // Then
        verify(memberBrandAttentionRepository).findByMemberId(eq(1L), argThat(pageable -> 
            pageable.getPageNumber() == 1 && // pageNum - 1
            pageable.getPageSize() == 5
        ));
    }

    @Test
    void testDetail_Success() {
        // Given
        Long brandId = 1L;
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberIdAndBrandId(1L, brandId)).thenReturn(mockAttention);

        // When
        MemberBrandAttention result = memberAttentionService.detail(brandId);

        // Then
        assertNotNull(result);
        assertEquals(mockAttention, result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberIdAndBrandId(1L, brandId);
    }

    @Test
    void testDetail_NotFound() {
        // Given
        Long brandId = 999L;
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(memberBrandAttentionRepository.findByMemberIdAndBrandId(1L, brandId)).thenReturn(null);

        // When
        MemberBrandAttention result = memberAttentionService.detail(brandId);

        // Then
        assertNull(result);
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).findByMemberIdAndBrandId(1L, brandId);
    }

    @Test
    void testClear_Success() {
        // Given
        when(memberService.getCurrentMember()).thenReturn(mockMember);

        // When
        memberAttentionService.clear();

        // Then
        verify(memberService).getCurrentMember();
        verify(memberBrandAttentionRepository).deleteAllByMemberId(1L);
    }
}