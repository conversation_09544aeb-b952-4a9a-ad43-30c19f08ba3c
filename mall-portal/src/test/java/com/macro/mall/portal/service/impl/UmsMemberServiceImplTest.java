package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.UmsMemberLevelMapper;
import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.model.UmsMemberLevel;
import com.macro.mall.portal.domain.MemberDetails;
import com.macro.mall.portal.service.UmsMemberCacheService;
import com.macro.mall.security.util.JwtTokenUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UmsMemberServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class UmsMemberServiceImplTest {

    @Mock
    private PasswordEncoder passwordEncoder;
    @Mock
    private JwtTokenUtil jwtTokenUtil;
    @Mock
    private UmsMemberMapper memberMapper;
    @Mock
    private UmsMemberLevelMapper memberLevelMapper;
    @Mock
    private UmsMemberCacheService memberCacheService;
    @InjectMocks
    private UmsMemberServiceImpl memberService;

    private UmsMember mockMember;
    private UmsMemberLevel mockMemberLevel;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(memberService, "REDIS_KEY_PREFIX_AUTH_CODE", "authCode");
        ReflectionTestUtils.setField(memberService, "AUTH_CODE_EXPIRE_SECONDS", 600L);

        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setUsername("testUser");
        mockMember.setPassword("encodedPassword");
        mockMember.setPhone("13800138000");

        mockMemberLevel = new UmsMemberLevel();
        mockMemberLevel.setId(1L);
        mockMemberLevel.setDefaultStatus(1);
    }

    @Test
    void testGetByUsername_FromCache() {
        when(memberCacheService.getMember("testUser")).thenReturn(mockMember);

        UmsMember result = memberService.getByUsername("testUser");
        
        assertEquals(mockMember, result);
        verify(memberCacheService).getMember("testUser");
        verify(memberMapper, never()).selectByExample(any());
    }

    @Test
    void testGetByUsername_FromDatabase() {
        when(memberCacheService.getMember("testUser")).thenReturn(null);
        when(memberMapper.selectByExample(any())).thenReturn(Arrays.asList(mockMember));

        UmsMember result = memberService.getByUsername("testUser");
        
        assertEquals(mockMember, result);
        verify(memberCacheService).setMember(mockMember);
    }

    @Test
    void testGetById_Success() {
        when(memberMapper.selectByPrimaryKey(1L)).thenReturn(mockMember);

        UmsMember result = memberService.getById(1L);
        
        assertEquals(mockMember, result);
    }

    @Test
    void testRegister_Success() {
        when(memberCacheService.getAuthCode("13800138000")).thenReturn("123456");
        when(memberMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(passwordEncoder.encode("password")).thenReturn("encodedPassword");
        when(memberLevelMapper.selectByExample(any())).thenReturn(Arrays.asList(mockMemberLevel));
        when(memberMapper.insert(any())).thenReturn(1);

        assertDoesNotThrow(() -> {
            memberService.register("testUser", "password", "13800138000", "123456");
        });
        
        verify(memberMapper).insert(any());
    }

    @Test
    void testGenerateAuthCode_Success() {
        String result = memberService.generateAuthCode("13800138000");
        
        assertEquals(6, result.length());
        assertTrue(result.matches("\\d{6}"));
        verify(memberCacheService).setAuthCode(eq("13800138000"), anyString());
    }

    @Test
    void testUpdatePassword_Success() {
        when(memberMapper.selectByExample(any())).thenReturn(Arrays.asList(mockMember));
        when(memberCacheService.getAuthCode("13800138000")).thenReturn("123456");
        when(passwordEncoder.encode("newPassword")).thenReturn("encodedNewPassword");
        when(memberMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        assertDoesNotThrow(() -> {
            memberService.updatePassword("13800138000", "newPassword", "123456");
        });
        
        verify(memberMapper).updateByPrimaryKeySelective(any());
        verify(memberCacheService).delMember(1L);
    }

    @Test
    void testUpdateIntegration_Success() {
        when(memberMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        memberService.updateIntegration(1L, 100);
        
        verify(memberMapper).updateByPrimaryKeySelective(any());
        verify(memberCacheService).delMember(1L);
    }

    @Test
    void testLoadUserByUsername_Success() {
        when(memberCacheService.getMember("testUser")).thenReturn(mockMember);

        UserDetails result = memberService.loadUserByUsername("testUser");
        
        assertNotNull(result);
        assertTrue(result instanceof MemberDetails);
        assertEquals("testUser", result.getUsername());
    }

    @Test
    void testLoadUserByUsername_NotFound() {
        when(memberCacheService.getMember("testUser")).thenReturn(null);
        when(memberMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        assertThrows(UsernameNotFoundException.class, () -> {
            memberService.loadUserByUsername("testUser");
        });
    }

    @Test
    void testLogin_Success() {
        when(memberCacheService.getMember("testUser")).thenReturn(mockMember);
        when(passwordEncoder.matches("password", "encodedPassword")).thenReturn(true);
        when(jwtTokenUtil.generateToken(any())).thenReturn("jwt-token");

        String token = memberService.login("testUser", "password");
        
        assertEquals("jwt-token", token);
    }

    @Test
    void testLogin_WrongPassword() {
        when(memberCacheService.getMember("testUser")).thenReturn(mockMember);
        when(passwordEncoder.matches("wrongPassword", "encodedPassword")).thenReturn(false);

        String token = memberService.login("testUser", "wrongPassword");
        
        assertNull(token);
    }

    @Test
    void testRefreshToken_Success() {
        when(jwtTokenUtil.refreshHeadToken("old-token")).thenReturn("new-token");

        String result = memberService.refreshToken("old-token");
        
        assertEquals("new-token", result);
    }
}