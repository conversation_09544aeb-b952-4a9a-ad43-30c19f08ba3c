package com.macro.mall.portal.service.impl;

import com.github.pagehelper.PageHelper;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.mapper.PmsBrandMapper;
import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.model.PmsBrand;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.portal.dao.HomeDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PmsPortalBrandServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class PmsPortalBrandServiceImplTest {

    @Mock
    private HomeDao homeDao;
    @Mock
    private PmsBrandMapper brandMapper;
    @Mock
    private PmsProductMapper productMapper;
    @InjectMocks
    private PmsPortalBrandServiceImpl brandService;

    private PmsBrand mockBrand;
    private List<PmsProduct> mockProductList;

    @BeforeEach
    void setUp() {
        mockBrand = new PmsBrand();
        mockBrand.setId(1L);
        mockBrand.setName("测试品牌");

        mockProductList = new ArrayList<>();
        PmsProduct product = new PmsProduct();
        product.setId(1L);
        product.setName("测试商品");
        mockProductList.add(product);
    }

    @Test
    void testRecommendList_Success() {
        List<PmsBrand> brandList = new ArrayList<>();
        brandList.add(mockBrand);
        
        when(homeDao.getRecommendBrandList(0, 10)).thenReturn(brandList);

        List<PmsBrand> result = brandService.recommendList(1, 10);
        
        assertEquals(1, result.size());
        assertEquals(mockBrand, result.get(0));
        verify(homeDao).getRecommendBrandList(0, 10);
    }

    @Test
    void testRecommendList_PageCalculation() {
        when(homeDao.getRecommendBrandList(anyInt(), anyInt())).thenReturn(new ArrayList<>());

        // 测试页码计算: (pageNum - 1) * pageSize
        brandService.recommendList(3, 5);
        verify(homeDao).getRecommendBrandList(10, 5); // (3-1)*5 = 10
    }

    @Test
    void testDetail_Success() {
        when(brandMapper.selectByPrimaryKey(1L)).thenReturn(mockBrand);

        PmsBrand result = brandService.detail(1L);
        
        assertEquals(mockBrand, result);
        verify(brandMapper).selectByPrimaryKey(1L);
    }

    @Test
    void testDetail_NotFound() {
        when(brandMapper.selectByPrimaryKey(999L)).thenReturn(null);

        PmsBrand result = brandService.detail(999L);
        
        assertNull(result);
        verify(brandMapper).selectByPrimaryKey(999L);
    }

    @Test
    void testProductList_Success() {
        when(productMapper.selectByExample(any())).thenReturn(mockProductList);

        try (MockedStatic<PageHelper> mockedPageHelper = mockStatic(PageHelper.class);
             MockedStatic<CommonPage> mockedCommonPage = mockStatic(CommonPage.class)) {
            
            CommonPage<PmsProduct> expectedPage = new CommonPage<>();
            mockedCommonPage.when(() -> CommonPage.restPage(mockProductList)).thenReturn(expectedPage);

            CommonPage<PmsProduct> result = brandService.productList(1L, 1, 10);

            assertEquals(expectedPage, result);
            
            // 在try块中验证MockedStatic
            mockedPageHelper.verify(() -> PageHelper.startPage(1, 10));
        }
        
        // 简化验证
        verify(productMapper, times(1)).selectByExample(any());
    }

    @Test
    void testProductList_EmptyResult() {
        when(productMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        try (MockedStatic<PageHelper> mockedPageHelper = mockStatic(PageHelper.class);
             MockedStatic<CommonPage> mockedCommonPage = mockStatic(CommonPage.class)) {
            
            CommonPage<PmsProduct> expectedPage = new CommonPage<>();
            mockedCommonPage.when(() -> CommonPage.restPage(any(List.class))).thenReturn(expectedPage);

            CommonPage<PmsProduct> result = brandService.productList(1L, 1, 10);

            assertNotNull(result);
            
            // 在try块中验证MockedStatic
            mockedPageHelper.verify(() -> PageHelper.startPage(1, 10));
        }
        
        // 简化验证
        verify(productMapper, times(1)).selectByExample(any());
    }
}