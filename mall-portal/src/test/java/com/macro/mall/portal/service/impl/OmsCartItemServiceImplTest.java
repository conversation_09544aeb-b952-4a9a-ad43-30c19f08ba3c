package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.OmsCartItemMapper;
import com.macro.mall.model.OmsCartItem;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.dao.PortalProductDao;
import com.macro.mall.portal.domain.CartProduct;
import com.macro.mall.portal.domain.CartPromotionItem;
import com.macro.mall.portal.service.OmsPromotionService;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OmsCartItemServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class OmsCartItemServiceImplTest {

    @Mock
    private OmsCartItemMapper cartItemMapper;
    @Mock
    private PortalProductDao productDao;
    @Mock
    private OmsPromotionService promotionService;
    @Mock
    private UmsMemberService memberService;
    @InjectMocks
    private OmsCartItemServiceImpl cartItemService;

    private UmsMember mockMember;
    private OmsCartItem mockCartItem;

    @BeforeEach
    void setUp() {
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setNickname("testUser");

        mockCartItem = new OmsCartItem();
        mockCartItem.setId(1L);
        mockCartItem.setMemberId(1L);
        mockCartItem.setProductId(1L);
        mockCartItem.setQuantity(2);
    }

    @Test
    void testAdd_NewItem() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(cartItemMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(cartItemMapper.insert(any())).thenReturn(1);

        int result = cartItemService.add(mockCartItem);
        assertEquals(1, result);
        verify(cartItemMapper).insert(any());
    }

    @Test
    void testAdd_ExistingItem() {
        List<OmsCartItem> existingItems = Arrays.asList(mockCartItem);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(cartItemMapper.selectByExample(any())).thenReturn(existingItems);
        when(cartItemMapper.updateByPrimaryKey(any())).thenReturn(1);

        OmsCartItem newItem = new OmsCartItem();
        newItem.setProductId(1L);
        newItem.setQuantity(3);

        int result = cartItemService.add(newItem);
        assertEquals(1, result);
        verify(cartItemMapper).updateByPrimaryKey(any());
    }

    @Test
    void testList_Success() {
        List<OmsCartItem> cartItems = Arrays.asList(mockCartItem);
        when(cartItemMapper.selectByExample(any())).thenReturn(cartItems);

        List<OmsCartItem> result = cartItemService.list(1L);
        assertEquals(1, result.size());
    }

    @Test
    void testListPromotion_Success() {
        List<OmsCartItem> cartItems = Arrays.asList(mockCartItem);
        List<CartPromotionItem> promotionItems = new ArrayList<>();
        when(cartItemMapper.selectByExample(any())).thenReturn(cartItems);
        when(promotionService.calcCartPromotion(any())).thenReturn(promotionItems);

        List<CartPromotionItem> result = cartItemService.listPromotion(1L, Arrays.asList(1L));
        assertNotNull(result);
    }

    @Test
    void testUpdateQuantity_Success() {
        when(cartItemMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        
        int result = cartItemService.updateQuantity(1L, 1L, 5);
        assertEquals(1, result);
    }

    @Test
    void testDelete_Success() {
        when(cartItemMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        
        int result = cartItemService.delete(1L, Arrays.asList(1L, 2L));
        assertEquals(1, result);
    }

    @Test
    void testGetCartProduct_Success() {
        CartProduct cartProduct = new CartProduct();
        when(productDao.getCartProduct(1L)).thenReturn(cartProduct);
        
        CartProduct result = cartItemService.getCartProduct(1L);
        assertEquals(cartProduct, result);
    }

    @Test
    void testUpdateAttr_Success() {
        when(cartItemMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(cartItemMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(cartItemMapper.insert(any())).thenReturn(1);

        int result = cartItemService.updateAttr(mockCartItem);
        assertEquals(1, result);
    }

    @Test
    void testClear_Success() {
        when(cartItemMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        
        int result = cartItemService.clear(1L);
        assertEquals(1, result);
    }
}