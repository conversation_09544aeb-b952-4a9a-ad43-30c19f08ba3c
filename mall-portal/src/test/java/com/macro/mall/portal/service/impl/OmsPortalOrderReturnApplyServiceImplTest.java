package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.OmsOrderReturnApplyMapper;
import com.macro.mall.model.OmsOrderReturnApply;
import com.macro.mall.portal.domain.OmsOrderReturnApplyParam;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OmsPortalOrderReturnApplyServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class OmsPortalOrderReturnApplyServiceImplTest {

    @Mock
    private OmsOrderReturnApplyMapper returnApplyMapper;

    @InjectMocks
    private OmsPortalOrderReturnApplyServiceImpl orderReturnApplyService;

    private OmsOrderReturnApplyParam mockParam;

    @BeforeEach
    void setUp() {
        mockParam = new OmsOrderReturnApplyParam();
        mockParam.setOrderId(1L);
        mockParam.setProductId(1L);
        mockParam.setOrderSn("ORDER_001");
        mockParam.setReturnName("退货测试");
        mockParam.setReturnPhone("13800138000");
        mockParam.setReason("质量问题");
        mockParam.setDescription("商品有质量问题，申请退货");
    }

    @Test
    void testCreate_Success() {
        // Given
        when(returnApplyMapper.insert(any(OmsOrderReturnApply.class))).thenReturn(1);

        // When
        int result = orderReturnApplyService.create(mockParam);

        // Then
        assertEquals(1, result);
        verify(returnApplyMapper).insert(argThat(apply -> 
            apply.getOrderId().equals(1L) &&
            apply.getProductId().equals(1L) &&
            apply.getOrderSn().equals("ORDER_001") &&
            apply.getReturnName().equals("退货测试") &&
            apply.getReturnPhone().equals("13800138000") &&
            apply.getReason().equals("质量问题") &&
            apply.getDescription().equals("商品有质量问题，申请退货") &&
            apply.getCreateTime() != null &&
            apply.getStatus().equals(0)
        ));
    }

    @Test
    void testCreate_WithNullValues() {
        // Given
        OmsOrderReturnApplyParam paramWithNulls = new OmsOrderReturnApplyParam();
        paramWithNulls.setOrderId(2L);
        paramWithNulls.setProductId(2L);
        // 其他字段保持null
        
        when(returnApplyMapper.insert(any(OmsOrderReturnApply.class))).thenReturn(1);

        // When
        int result = orderReturnApplyService.create(paramWithNulls);

        // Then
        assertEquals(1, result);
        verify(returnApplyMapper).insert(argThat(apply -> 
            apply.getOrderId().equals(2L) &&
            apply.getProductId().equals(2L) &&
            apply.getCreateTime() != null &&
            apply.getStatus().equals(0)
        ));
    }

    @Test
    void testCreate_MapperThrowsException() {
        // Given
        when(returnApplyMapper.insert(any(OmsOrderReturnApply.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            orderReturnApplyService.create(mockParam);
        });
        
        verify(returnApplyMapper).insert(any(OmsOrderReturnApply.class));
    }
}