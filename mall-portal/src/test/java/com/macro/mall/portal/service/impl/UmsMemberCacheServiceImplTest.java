package com.macro.mall.portal.service.impl;

import com.macro.mall.common.service.RedisService;
import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UmsMemberCacheServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class UmsMemberCacheServiceImplTest {

    @Mock
    private RedisService redisService;
    @Mock
    private UmsMemberMapper memberMapper;
    @InjectMocks
    private UmsMemberCacheServiceImpl memberCacheService;

    private UmsMember mockMember;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(memberCacheService, "REDIS_DATABASE", "mall");
        ReflectionTestUtils.setField(memberCacheService, "REDIS_EXPIRE", 3600L);
        ReflectionTestUtils.setField(memberCacheService, "REDIS_EXPIRE_AUTH_CODE", 600L);
        ReflectionTestUtils.setField(memberCacheService, "REDIS_KEY_MEMBER", "member");
        ReflectionTestUtils.setField(memberCacheService, "REDIS_KEY_AUTH_CODE", "authCode");

        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setUsername("testUser");
    }

    @Test
    void testDelMember_Success() {
        when(memberMapper.selectByPrimaryKey(1L)).thenReturn(mockMember);
        memberCacheService.delMember(1L);
        verify(redisService).del("mall:member:testUser");
    }

    @Test
    void testGetMember_Success() {
        when(redisService.get("mall:member:testUser")).thenReturn(mockMember);
        UmsMember result = memberCacheService.getMember("testUser");
        assertEquals(mockMember, result);
    }

    @Test
    void testSetMember_Success() {
        memberCacheService.setMember(mockMember);
        verify(redisService).set("mall:member:testUser", mockMember, 3600L);
    }

    @Test
    void testSetAuthCode_Success() {
        memberCacheService.setAuthCode("13800138000", "123456");
        verify(redisService).set("mall:authCode:13800138000", "123456", 600L);
    }

    @Test
    void testGetAuthCode_Success() {
        when(redisService.get("mall:authCode:13800138000")).thenReturn("123456");
        String result = memberCacheService.getAuthCode("13800138000");
        assertEquals("123456", result);
    }
}