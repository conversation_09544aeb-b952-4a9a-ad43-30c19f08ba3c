package com.macro.mall.portal.service.impl;

import com.github.pagehelper.PageHelper;
import com.macro.mall.mapper.PmsBrandMapper;
import com.macro.mall.mapper.PmsProductAttributeMapper;
import com.macro.mall.mapper.PmsProductAttributeValueMapper;
import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.mapper.PmsSkuStockMapper;
import com.macro.mall.model.PmsBrand;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.model.PmsProductAttribute;
import com.macro.mall.model.PmsProductAttributeValue;
import com.macro.mall.portal.domain.PmsPortalProductDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PmsPortalProductServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class PmsPortalProductServiceImplTest {

    @Mock
    private PmsProductMapper productMapper;
    @Mock
    private PmsBrandMapper brandMapper;
    @Mock
    private PmsProductAttributeMapper productAttributeMapper;
    @Mock
    private PmsProductAttributeValueMapper productAttributeValueMapper;
    @Mock
    private PmsSkuStockMapper skuStockMapper;

    @InjectMocks
    private PmsPortalProductServiceImpl productService;

    private PmsProduct mockProduct;

    @BeforeEach
    void setUp() {
        mockProduct = new PmsProduct();
        mockProduct.setId(1L);
        mockProduct.setName("测试商品");
        mockProduct.setPrice(new BigDecimal("100.00"));
        mockProduct.setBrandId(1L);
        mockProduct.setProductAttributeCategoryId(1L);
        mockProduct.setDeleteStatus(0);
        mockProduct.setPublishStatus(1);
        mockProduct.setPromotionType(0); // 设置促销类型，避免NullPointer
    }

    @Test
    void testSearch_Success() {
        List<PmsProduct> mockProductList = new ArrayList<>();
        mockProductList.add(mockProduct);
        
        when(productMapper.selectByExample(any())).thenReturn(mockProductList);

        try (MockedStatic<PageHelper> mockedPageHelper = mockStatic(PageHelper.class)) {
            List<PmsProduct> result = productService.search("测试", 1L, 1L, 1, 10, 1);
            
            assertNotNull(result);
            assertEquals(1, result.size());
            
            // 在try块中验证MockedStatic
            mockedPageHelper.verify(() -> PageHelper.startPage(1, 10));
        }
        
        // 简化验证
        verify(productMapper, times(1)).selectByExample(any());
    }

    @Test
    void testDetail_Success() {
        List<PmsProductAttribute> attributeList = new ArrayList<>();
        PmsProductAttribute attribute = new PmsProductAttribute();
        attribute.setId(1L);
        attribute.setName("测试属性");
        attributeList.add(attribute);
        
        List<PmsProductAttributeValue> attributeValueList = new ArrayList<>();
        PmsProductAttributeValue attributeValue = new PmsProductAttributeValue();
        attributeValue.setId(1L);
        attributeValue.setValue("测试值");
        attributeValueList.add(attributeValue);

        when(productMapper.selectByPrimaryKey(1L)).thenReturn(mockProduct);
        when(brandMapper.selectByPrimaryKey(1L)).thenReturn(new PmsBrand());
        when(productAttributeMapper.selectByExample(any())).thenReturn(attributeList);
        when(productAttributeValueMapper.selectByExample(any())).thenReturn(attributeValueList);
        when(skuStockMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        PmsPortalProductDetail result = productService.detail(1L);
        
        assertNotNull(result);
        assertEquals(mockProduct, result.getProduct());
        verify(productMapper).selectByPrimaryKey(1L);
        verify(brandMapper).selectByPrimaryKey(1L);
        verify(productAttributeMapper).selectByExample(any());
        verify(productAttributeValueMapper).selectByExample(any());
        verify(skuStockMapper).selectByExample(any());
    }
}