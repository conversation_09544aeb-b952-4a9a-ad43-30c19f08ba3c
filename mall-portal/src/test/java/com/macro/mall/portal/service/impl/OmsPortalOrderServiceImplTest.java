package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.OmsOrderMapper;
import com.macro.mall.mapper.OmsOrderSettingMapper;
import com.macro.mall.mapper.UmsIntegrationConsumeSettingMapper;
import com.macro.mall.model.OmsOrderSetting;
import com.macro.mall.model.UmsIntegrationConsumeSetting;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.domain.ConfirmOrderResult;
import com.macro.mall.portal.domain.OrderParam;
import com.macro.mall.portal.service.OmsCartItemService;
import com.macro.mall.portal.service.UmsMemberCouponService;
import com.macro.mall.portal.service.UmsMemberReceiveAddressService;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OmsPortalOrderServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class OmsPortalOrderServiceImplTest {

    @Mock
    private UmsMemberService memberService;
    @Mock
    private OmsCartItemService cartItemService;
    @Mock
    private UmsMemberReceiveAddressService memberReceiveAddressService;
    @Mock
    private UmsMemberCouponService memberCouponService;
    @Mock
    private UmsIntegrationConsumeSettingMapper integrationConsumeSettingMapper;
    @Mock
    private OmsOrderMapper orderMapper;
    @Mock
    private OmsOrderSettingMapper orderSettingMapper;

    @InjectMocks
    private OmsPortalOrderServiceImpl orderService;

    private UmsMember mockMember;
    private OrderParam mockOrderParam;

    @BeforeEach
    void setUp() {
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setUsername("testUser");
        mockMember.setIntegration(1000);

        mockOrderParam = new OrderParam();
        mockOrderParam.setMemberReceiveAddressId(1L);
        mockOrderParam.setCartIds(Arrays.asList(1L, 2L));
        mockOrderParam.setPayType(1);
    }

    @Test
    void testGenerateConfirmOrder_Success() {
        UmsIntegrationConsumeSetting setting = new UmsIntegrationConsumeSetting();
        setting.setId(1L);
        setting.setDeductionPerAmount(100);
        setting.setMaxPercentPerOrder(50);
        
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(cartItemService.listPromotion(eq(1L), any())).thenReturn(new ArrayList<>());
        when(memberReceiveAddressService.list()).thenReturn(new ArrayList<>());
        when(memberCouponService.listCart(any(), eq(1))).thenReturn(new ArrayList<>());
        when(integrationConsumeSettingMapper.selectByPrimaryKey(1L)).thenReturn(setting);

        ConfirmOrderResult result = orderService.generateConfirmOrder(Arrays.asList(1L, 2L));
        
        assertNotNull(result);
        assertEquals(Integer.valueOf(1000), result.getMemberIntegration());
        verify(memberService).getCurrentMember();
    }

    @Test
    void testCancelTimeOutOrder_Success() {
        OmsOrderSetting setting = new OmsOrderSetting();
        setting.setId(1L);
        setting.setNormalOrderOvertime(60);
        
        when(orderSettingMapper.selectByPrimaryKey(1L)).thenReturn(setting);
        
        int result = orderService.cancelTimeOutOrder();
        
        assertTrue(result >= 0);
    }
}