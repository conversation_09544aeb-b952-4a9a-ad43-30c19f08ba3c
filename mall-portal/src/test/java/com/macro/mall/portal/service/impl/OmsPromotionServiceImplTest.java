package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.model.OmsCartItem;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.model.PmsSkuStock;
import com.macro.mall.portal.dao.PortalProductDao;
import com.macro.mall.portal.domain.CartPromotionItem;
import com.macro.mall.portal.domain.PromotionProduct;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OmsPromotionServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class OmsPromotionServiceImplTest {

    @Mock
    private PortalProductDao portalProductDao;

    @InjectMocks
    private OmsPromotionServiceImpl promotionService;

    private List<OmsCartItem> mockCartItems;
    private List<PromotionProduct> mockPromotionProducts;

    @BeforeEach
    void setUp() {
        mockCartItems = new ArrayList<>();
        OmsCartItem cartItem = new OmsCartItem();
        cartItem.setId(1L);
        cartItem.setProductId(1L);
        cartItem.setProductSkuId(1L);
        cartItem.setQuantity(2);
        cartItem.setPrice(new BigDecimal("100.00"));
        mockCartItems.add(cartItem);
        
        // 建立模拟PromotionProduct数据
        mockPromotionProducts = new ArrayList<>();
        PromotionProduct promotionProduct = new PromotionProduct();
        promotionProduct.setId(1L);
        promotionProduct.setPromotionType(0); // 无优惠
        promotionProduct.setGiftPoint(10);
        promotionProduct.setGiftGrowth(5);
        
        // 建立库存信息
        List<PmsSkuStock> skuStockList = new ArrayList<>();
        PmsSkuStock skuStock = new PmsSkuStock();
        skuStock.setId(1L);
        skuStock.setPrice(new BigDecimal("100.00"));
        skuStock.setStock(100);
        skuStock.setLockStock(0);
        skuStockList.add(skuStock);
        promotionProduct.setSkuStockList(skuStockList);
        
        mockPromotionProducts.add(promotionProduct);
    }

    @Test
    void testCalcCartPromotion_Success() {
        when(portalProductDao.getPromotionProductList(any())).thenReturn(mockPromotionProducts);

        List<CartPromotionItem> result = promotionService.calcCartPromotion(mockCartItems);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CartPromotionItem item = result.get(0);
        assertEquals(1L, item.getProductId());
        assertEquals("无优惠", item.getPromotionMessage());
        assertEquals(new BigDecimal("0"), item.getReduceAmount());
        assertEquals(Integer.valueOf(10), item.getIntegration());
        assertEquals(Integer.valueOf(5), item.getGrowth());
        
        verify(portalProductDao).getPromotionProductList(any());
    }

    @Test
    void testCalcCartPromotion_EmptyCart() {
        List<CartPromotionItem> result = promotionService.calcCartPromotion(new ArrayList<>());
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}