package com.macro.mall.portal.service.impl;

import com.macro.mall.mapper.UmsMemberReceiveAddressMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.model.UmsMemberReceiveAddress;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UmsMemberReceiveAddressServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class UmsMemberReceiveAddressServiceImplTest {

    @Mock
    private UmsMemberService memberService;
    @Mock
    private UmsMemberReceiveAddressMapper addressMapper;
    @InjectMocks
    private UmsMemberReceiveAddressServiceImpl addressService;

    private UmsMember mockMember;
    private UmsMemberReceiveAddress mockAddress;

    @BeforeEach
    void setUp() {
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setUsername("testUser");

        mockAddress = new UmsMemberReceiveAddress();
        mockAddress.setId(1L);
        mockAddress.setMemberId(1L);
        mockAddress.setName("张三");
        mockAddress.setPhoneNumber("13800138000");
        mockAddress.setDefaultStatus(0);
        mockAddress.setPostCode("100000");
        mockAddress.setProvince("北京市");
        mockAddress.setCity("北京市");
        mockAddress.setRegion("朝阳区");
        mockAddress.setDetailAddress("详细地址");
    }

    @Test
    void testAdd_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.insert(any())).thenReturn(1);

        int result = addressService.add(mockAddress);
        
        assertEquals(1, result);
        verify(addressMapper).insert(argThat(address -> 
            address.getMemberId().equals(1L)
        ));
    }

    @Test
    void testDelete_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.deleteByExample(any())).thenReturn(1);

        int result = addressService.delete(1L);
        
        assertEquals(1, result);
        verify(addressMapper).deleteByExample(any());
    }

    @Test
    void testUpdate_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        UmsMemberReceiveAddress updateAddress = new UmsMemberReceiveAddress();
        updateAddress.setName("李四");
        updateAddress.setDefaultStatus(0);

        int result = addressService.update(1L, updateAddress);
        
        assertEquals(1, result);
        verify(addressMapper).updateByExampleSelective(any(), any());
    }

    @Test
    void testUpdate_SetAsDefault() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        UmsMemberReceiveAddress updateAddress = new UmsMemberReceiveAddress();
        updateAddress.setDefaultStatus(1); // 设为默认地址

        int result = addressService.update(1L, updateAddress);
        
        assertEquals(1, result);
        verify(addressMapper, times(2)).updateByExampleSelective(any(), any());
    }

    @Test
    void testUpdate_NullDefaultStatus() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        UmsMemberReceiveAddress updateAddress = new UmsMemberReceiveAddress();
        updateAddress.setDefaultStatus(null);

        addressService.update(1L, updateAddress);
        
        verify(addressMapper).updateByExampleSelective(argThat(address -> 
            address.getDefaultStatus().equals(0)
        ), any());
    }

    @Test
    void testList_Success() {
        List<UmsMemberReceiveAddress> addressList = Arrays.asList(mockAddress);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.selectByExample(any())).thenReturn(addressList);

        List<UmsMemberReceiveAddress> result = addressService.list();
        
        assertEquals(1, result.size());
        assertEquals(mockAddress, result.get(0));
        verify(addressMapper).selectByExample(any());
    }

    @Test
    void testGetItem_Success() {
        List<UmsMemberReceiveAddress> addressList = Arrays.asList(mockAddress);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.selectByExample(any())).thenReturn(addressList);

        UmsMemberReceiveAddress result = addressService.getItem(1L);
        
        assertEquals(mockAddress, result);
        verify(addressMapper).selectByExample(any());
    }

    @Test
    void testGetItem_NotFound() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(addressMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        UmsMemberReceiveAddress result = addressService.getItem(999L);
        
        assertNull(result);
        verify(addressMapper).selectByExample(any());
    }
}