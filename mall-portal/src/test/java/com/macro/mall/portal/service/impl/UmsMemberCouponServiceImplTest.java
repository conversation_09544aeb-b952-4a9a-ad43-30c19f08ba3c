package com.macro.mall.portal.service.impl;

import com.github.pagehelper.PageHelper;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.mapper.SmsCouponHistoryMapper;
import com.macro.mall.mapper.SmsCouponMapper;
import com.macro.mall.mapper.SmsCouponProductRelationMapper;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.model.SmsCoupon;
import com.macro.mall.model.SmsCouponHistory;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.dao.SmsCouponHistoryDao;
import com.macro.mall.portal.domain.SmsCouponHistoryDetail;
import com.macro.mall.portal.service.UmsMemberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UmsMemberCouponServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class UmsMemberCouponServiceImplTest {

    @Mock
    private UmsMemberService memberService;
    @Mock
    private SmsCouponMapper couponMapper;
    @Mock
    private SmsCouponHistoryMapper couponHistoryMapper;
    @Mock
    private SmsCouponHistoryDao couponHistoryDao;
    @Mock
    private SmsCouponProductRelationMapper couponProductRelationMapper;
    @Mock
    private PmsProductMapper productMapper;
    @InjectMocks
    private UmsMemberCouponServiceImpl memberCouponService;

    private UmsMember mockMember;
    private SmsCoupon mockCoupon;
    private SmsCouponHistory mockCouponHistory;

    @BeforeEach
    void setUp() {
        mockMember = new UmsMember();
        mockMember.setId(1L);
        mockMember.setNickname("testUser");

        mockCoupon = new SmsCoupon();
        mockCoupon.setId(1L);
        mockCoupon.setName("测试优惠券");
        mockCoupon.setCount(100);
        mockCoupon.setPerLimit(1);
        mockCoupon.setStartTime(new Date());
        mockCoupon.setEndTime(new Date(System.currentTimeMillis() + 86400000)); // 一天后

        mockCouponHistory = new SmsCouponHistory();
        mockCouponHistory.setId(1L);
        mockCouponHistory.setCouponId(1L);
        mockCouponHistory.setMemberId(1L);
    }

    @Test
    void testAdd_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(couponMapper.selectByPrimaryKey(1L)).thenReturn(mockCoupon);
        when(couponHistoryMapper.countByExample(any())).thenReturn(0L);
        when(couponHistoryMapper.insert(any())).thenReturn(1);
        when(couponMapper.updateByPrimaryKey(any())).thenReturn(1);

        memberCouponService.add(1L);
        
        verify(couponMapper).selectByPrimaryKey(1L);
        verify(couponHistoryMapper).insert(any(SmsCouponHistory.class));
        verify(couponMapper).updateByPrimaryKey(any(SmsCoupon.class));
    }

    @Test
    void testListHistory_Success() {
        List<SmsCouponHistory> historyList = Arrays.asList(mockCouponHistory);
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        when(couponHistoryMapper.selectByExample(any())).thenReturn(historyList);

        List<SmsCouponHistory> result = memberCouponService.listHistory(1);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(memberService).getCurrentMember();
        verify(couponHistoryMapper).selectByExample(any());
    }

    @Test
    void testListCart_Success() {
        when(memberService.getCurrentMember()).thenReturn(mockMember);
        
        List<SmsCouponHistoryDetail> result = memberCouponService.listCart(new ArrayList<>(), 1);
        
        assertNotNull(result);
        verify(memberService).getCurrentMember();
    }

    @Test
    void testListByProduct_Success() {
        List<SmsCoupon> couponList = Arrays.asList(mockCoupon);
        
        // 创建PmsProduct对象并设置必要属性
        PmsProduct product = new PmsProduct();
        product.setId(1L);
        product.setProductCategoryId(1L); // 设置必要的属性
        product.setName("测试商品");
        
        when(couponMapper.selectByExample(any())).thenReturn(couponList);
        when(couponProductRelationMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(productMapper.selectByPrimaryKey(1L)).thenReturn(product);

        List<SmsCoupon> result = memberCouponService.listByProduct(1L);
        
        assertNotNull(result);
        verify(couponMapper).selectByExample(any());
        verify(couponProductRelationMapper).selectByExample(any());
        verify(productMapper).selectByPrimaryKey(1L);
    }
}