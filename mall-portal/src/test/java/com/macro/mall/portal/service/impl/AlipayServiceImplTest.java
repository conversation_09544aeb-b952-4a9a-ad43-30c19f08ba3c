package com.macro.mall.portal.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.macro.mall.mapper.OmsOrderMapper;
import com.macro.mall.portal.config.AlipayConfig;
import com.macro.mall.portal.domain.AliPayParam;
import com.macro.mall.portal.service.OmsPortalOrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AlipayServiceImpl单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AlipayServiceImplTest {

    @Mock
    private AlipayConfig alipayConfig;

    @Mock
    private AlipayClient alipayClient;

    @Mock
    private OmsOrderMapper orderMapper;

    @Mock
    private OmsPortalOrderService portalOrderService;

    @InjectMocks
    private AlipayServiceImpl alipayService;

    private AliPayParam aliPayParam;

    @BeforeEach
    void setUp() {
        aliPayParam = new AliPayParam();
        aliPayParam.setOutTradeNo("ORDER_001");
        aliPayParam.setTotalAmount(new BigDecimal("100.00"));
        aliPayParam.setSubject("测试商品");
    }

    @Test
    void testPay_Success() throws AlipayApiException {
        // Given
        when(alipayConfig.getNotifyUrl()).thenReturn("http://test.com/notify");
        when(alipayConfig.getReturnUrl()).thenReturn("http://test.com/return");
        
        AlipayTradePagePayResponse response = mock(AlipayTradePagePayResponse.class);
        when(response.getBody()).thenReturn("<form>test form</form>");
        when(alipayClient.pageExecute(any(AlipayTradePagePayRequest.class))).thenReturn(response);

        // When
        String result = alipayService.pay(aliPayParam);

        // Then
        assertNotNull(result);
        assertEquals("<form>test form</form>", result);
        verify(alipayClient).pageExecute(any(AlipayTradePagePayRequest.class));
    }

    @Test
    void testPay_WithNullUrls() throws AlipayApiException {
        // Given
        when(alipayConfig.getNotifyUrl()).thenReturn(null);
        when(alipayConfig.getReturnUrl()).thenReturn(null);
        
        AlipayTradePagePayResponse response = mock(AlipayTradePagePayResponse.class);
        when(response.getBody()).thenReturn("<form>test form</form>");
        when(alipayClient.pageExecute(any(AlipayTradePagePayRequest.class))).thenReturn(response);

        // When
        String result = alipayService.pay(aliPayParam);

        // Then
        assertNotNull(result);
        assertEquals("<form>test form</form>", result);
    }

    @Test
    void testPay_Exception() throws AlipayApiException {
        // Given
        when(alipayConfig.getNotifyUrl()).thenReturn("http://test.com/notify");
        when(alipayConfig.getReturnUrl()).thenReturn("http://test.com/return");
        when(alipayClient.pageExecute(any(AlipayTradePagePayRequest.class)))
                .thenThrow(new AlipayApiException("支付宝API异常"));

        // When
        String result = alipayService.pay(aliPayParam);

        // Then
        assertNull(result);
    }

    @Test
    void testNotify_Success() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("trade_status", "TRADE_SUCCESS");
        params.put("out_trade_no", "ORDER_001");

        when(alipayConfig.getAlipayPublicKey()).thenReturn("test_public_key");
        when(alipayConfig.getCharset()).thenReturn("UTF-8");
        when(alipayConfig.getSignType()).thenReturn("RSA2");

        try (MockedStatic<AlipaySignature> mockedSignature = mockStatic(AlipaySignature.class)) {
            mockedSignature.when(() -> AlipaySignature.rsaCheckV1(
                    eq(params), 
                    eq("test_public_key"), 
                    eq("UTF-8"), 
                    eq("RSA2")
            )).thenReturn(true);

            // When
            String result = alipayService.notify(params);

            // Then
            assertEquals("success", result);
            verify(portalOrderService).paySuccessByOrderSn("ORDER_001", 1);
        }
    }

    @Test
    void testNotify_FailedSignature() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("trade_status", "TRADE_SUCCESS");
        params.put("out_trade_no", "ORDER_001");

        when(alipayConfig.getAlipayPublicKey()).thenReturn("test_public_key");
        when(alipayConfig.getCharset()).thenReturn("UTF-8");
        when(alipayConfig.getSignType()).thenReturn("RSA2");

        try (MockedStatic<AlipaySignature> mockedSignature = mockStatic(AlipaySignature.class)) {
            mockedSignature.when(() -> AlipaySignature.rsaCheckV1(
                    eq(params), 
                    eq("test_public_key"), 
                    eq("UTF-8"), 
                    eq("RSA2")
            )).thenReturn(false);

            // When
            String result = alipayService.notify(params);

            // Then
            assertEquals("failure", result);
            verify(portalOrderService, never()).paySuccessByOrderSn(anyString(), anyInt());
        }
    }

    @Test
    void testNotify_NonSuccessTradeStatus() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("trade_status", "TRADE_CLOSED");
        params.put("out_trade_no", "ORDER_001");

        when(alipayConfig.getAlipayPublicKey()).thenReturn("test_public_key");
        when(alipayConfig.getCharset()).thenReturn("UTF-8");
        when(alipayConfig.getSignType()).thenReturn("RSA2");

        try (MockedStatic<AlipaySignature> mockedSignature = mockStatic(AlipaySignature.class)) {
            mockedSignature.when(() -> AlipaySignature.rsaCheckV1(
                    eq(params), 
                    eq("test_public_key"), 
                    eq("UTF-8"), 
                    eq("RSA2")
            )).thenReturn(true);

            // When
            String result = alipayService.notify(params);

            // Then
            assertEquals("failure", result);
            verify(portalOrderService, never()).paySuccessByOrderSn(anyString(), anyInt());
        }
    }

    @Test
    void testNotify_SignatureException() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("trade_status", "TRADE_SUCCESS");
        params.put("out_trade_no", "ORDER_001");

        when(alipayConfig.getAlipayPublicKey()).thenReturn("test_public_key");
        when(alipayConfig.getCharset()).thenReturn("UTF-8");
        when(alipayConfig.getSignType()).thenReturn("RSA2");

        try (MockedStatic<AlipaySignature> mockedSignature = mockStatic(AlipaySignature.class)) {
            mockedSignature.when(() -> AlipaySignature.rsaCheckV1(
                    eq(params), 
                    eq("test_public_key"), 
                    eq("UTF-8"), 
                    eq("RSA2")
            )).thenThrow(new AlipayApiException("签名验证异常"));

            // When
            String result = alipayService.notify(params);

            // Then
            assertEquals("failure", result);
            verify(portalOrderService, never()).paySuccessByOrderSn(anyString(), anyInt());
        }
    }

    @Test
    void testQuery_Success_WithOutTradeNo() throws AlipayApiException {
        // Given
        String outTradeNo = "ORDER_001";
        String tradeNo = null;

        AlipayTradeQueryResponse response = mock(AlipayTradeQueryResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getTradeStatus()).thenReturn("TRADE_SUCCESS");
        when(alipayClient.execute(any(AlipayTradeQueryRequest.class))).thenReturn(response);

        // When
        String result = alipayService.query(outTradeNo, tradeNo);

        // Then
        assertEquals("TRADE_SUCCESS", result);
        verify(portalOrderService).paySuccessByOrderSn(outTradeNo, 1);
    }

    @Test
    void testQuery_Success_WithTradeNo() throws AlipayApiException {
        // Given
        String outTradeNo = null;
        String tradeNo = "TRADE_001";

        AlipayTradeQueryResponse response = mock(AlipayTradeQueryResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getTradeStatus()).thenReturn("TRADE_SUCCESS");
        when(alipayClient.execute(any(AlipayTradeQueryRequest.class))).thenReturn(response);

        // When
        String result = alipayService.query(outTradeNo, tradeNo);

        // Then
        assertEquals("TRADE_SUCCESS", result);
        verify(portalOrderService, never()).paySuccessByOrderSn(anyString(), anyInt());
    }

    @Test
    void testQuery_Failed() throws AlipayApiException {
        // Given
        String outTradeNo = "ORDER_001";
        String tradeNo = null;

        AlipayTradeQueryResponse response = mock(AlipayTradeQueryResponse.class);
        when(response.isSuccess()).thenReturn(false);
        when(response.getTradeStatus()).thenReturn("TRADE_CLOSED");
        when(alipayClient.execute(any(AlipayTradeQueryRequest.class))).thenReturn(response);

        // When
        String result = alipayService.query(outTradeNo, tradeNo);

        // Then
        assertEquals("TRADE_CLOSED", result);
        verify(portalOrderService, never()).paySuccessByOrderSn(anyString(), anyInt());
    }

    @Test
    void testQuery_Exception() throws AlipayApiException {
        // Given
        String outTradeNo = "ORDER_001";
        String tradeNo = null;

        when(alipayClient.execute(any(AlipayTradeQueryRequest.class)))
                .thenThrow(new AlipayApiException("查询异常"));

        // When
        String result = alipayService.query(outTradeNo, tradeNo);

        // Then
        assertNull(result);
        verify(alipayClient).execute(any(AlipayTradeQueryRequest.class));
    }

    @Test
    void testWebPay_Success() throws AlipayApiException {
        // Given
        when(alipayConfig.getNotifyUrl()).thenReturn("http://test.com/notify");
        when(alipayConfig.getReturnUrl()).thenReturn("http://test.com/return");
        
        AlipayTradeWapPayResponse response = mock(AlipayTradeWapPayResponse.class);
        when(response.getBody()).thenReturn("<form>wap test form</form>");
        when(alipayClient.pageExecute(any(AlipayTradeWapPayRequest.class))).thenReturn(response);

        // When
        String result = alipayService.webPay(aliPayParam);

        // Then
        assertNotNull(result);
        assertEquals("<form>wap test form</form>", result);
        verify(alipayClient).pageExecute(any(AlipayTradeWapPayRequest.class));
    }

    @Test
    void testWebPay_WithNullUrls() throws AlipayApiException {
        // Given
        when(alipayConfig.getNotifyUrl()).thenReturn(null);
        when(alipayConfig.getReturnUrl()).thenReturn(null);
        
        AlipayTradeWapPayResponse response = mock(AlipayTradeWapPayResponse.class);
        when(response.getBody()).thenReturn("<form>wap test form</form>");
        when(alipayClient.pageExecute(any(AlipayTradeWapPayRequest.class))).thenReturn(response);

        // When
        String result = alipayService.webPay(aliPayParam);

        // Then
        assertNotNull(result);
        assertEquals("<form>wap test form</form>", result);
    }

    @Test
    void testWebPay_Exception() throws AlipayApiException {
        // Given
        when(alipayConfig.getNotifyUrl()).thenReturn("http://test.com/notify");
        when(alipayConfig.getReturnUrl()).thenReturn("http://test.com/return");
        when(alipayClient.pageExecute(any(AlipayTradeWapPayRequest.class)))
                .thenThrow(new AlipayApiException("WAP支付API异常"));

        // When
        String result = alipayService.webPay(aliPayParam);

        // Then
        assertNull(result);
    }
}