package com.macro.mall.portal.service.impl;

import com.github.pagehelper.PageHelper;
import com.macro.mall.mapper.*;
import com.macro.mall.model.*;
import com.macro.mall.portal.dao.HomeDao;
import com.macro.mall.portal.domain.FlashPromotionProduct;
import com.macro.mall.portal.domain.HomeContentResult;
import com.macro.mall.portal.domain.HomeFlashPromotion;
import com.macro.mall.portal.util.DateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * HomeServiceImpl单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class HomeServiceImplTest {

    @Mock
    private SmsHomeAdvertiseMapper advertiseMapper;

    @Mock
    private HomeDao homeDao;

    @Mock
    private SmsFlashPromotionMapper flashPromotionMapper;

    @Mock
    private SmsFlashPromotionSessionMapper promotionSessionMapper;

    @Mock
    private PmsProductMapper productMapper;

    @Mock
    private PmsProductCategoryMapper productCategoryMapper;

    @Mock
    private CmsSubjectMapper subjectMapper;

    @InjectMocks
    private HomeServiceImpl homeService;

    private Date testDate;
    private List<SmsHomeAdvertise> mockAdvertiseList;
    private List<PmsBrand> mockBrandList;
    private List<PmsProduct> mockProductList;
    private List<CmsSubject> mockSubjectList;

    @BeforeEach
    void setUp() {
        testDate = new Date();
        
        // Mock advertise list
        mockAdvertiseList = new ArrayList<>();
        SmsHomeAdvertise advertise = new SmsHomeAdvertise();
        advertise.setId(1L);
        advertise.setName("测试广告");
        mockAdvertiseList.add(advertise);

        // Mock brand list
        mockBrandList = new ArrayList<>();
        PmsBrand brand = new PmsBrand();
        brand.setId(1L);
        brand.setName("测试品牌");
        mockBrandList.add(brand);

        // Mock product list
        mockProductList = new ArrayList<>();
        PmsProduct product = new PmsProduct();
        product.setId(1L);
        product.setName("测试商品");
        mockProductList.add(product);

        // Mock subject list
        mockSubjectList = new ArrayList<>();
        CmsSubject subject = new CmsSubject();
        subject.setId(1L);
        subject.setTitle("测试专题");
        mockSubjectList.add(subject);
    }

    @Test
    void testContent_Success() {
        // Given
        when(advertiseMapper.selectByExample(any(SmsHomeAdvertiseExample.class)))
                .thenReturn(mockAdvertiseList);
        when(homeDao.getRecommendBrandList(0, 6)).thenReturn(mockBrandList);
        when(homeDao.getNewProductList(0, 4)).thenReturn(mockProductList);
        when(homeDao.getHotProductList(0, 4)).thenReturn(mockProductList);
        when(homeDao.getRecommendSubjectList(0, 4)).thenReturn(mockSubjectList);
        
        // Mock flash promotion
        when(flashPromotionMapper.selectByExample(any(SmsFlashPromotionExample.class)))
                .thenReturn(new ArrayList<>());

        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            mockedDateUtil.when(() -> DateUtil.getDate(any(Date.class))).thenReturn(testDate);
            mockedDateUtil.when(() -> DateUtil.getTime(any(Date.class))).thenReturn(testDate);

            // When
            HomeContentResult result = homeService.content();

            // Then
            assertNotNull(result);
            assertNotNull(result.getAdvertiseList());
            assertNotNull(result.getBrandList());
            assertNotNull(result.getNewProductList());
            assertNotNull(result.getHotProductList());
            assertNotNull(result.getSubjectList());
            assertEquals(1, result.getAdvertiseList().size());
            assertEquals(1, result.getBrandList().size());
            assertEquals(1, result.getNewProductList().size());
            assertEquals(1, result.getHotProductList().size());
            assertEquals(1, result.getSubjectList().size());

            verify(advertiseMapper).selectByExample(any(SmsHomeAdvertiseExample.class));
            verify(homeDao).getRecommendBrandList(0, 6);
            verify(homeDao).getNewProductList(0, 4);
            verify(homeDao).getHotProductList(0, 4);
            verify(homeDao).getRecommendSubjectList(0, 4);
        }
    }

    @Test
    void testContent_WithFlashPromotion() {
        // Given
        when(advertiseMapper.selectByExample(any(SmsHomeAdvertiseExample.class)))
                .thenReturn(mockAdvertiseList);
        when(homeDao.getRecommendBrandList(0, 6)).thenReturn(mockBrandList);
        when(homeDao.getNewProductList(0, 4)).thenReturn(mockProductList);
        when(homeDao.getHotProductList(0, 4)).thenReturn(mockProductList);
        when(homeDao.getRecommendSubjectList(0, 4)).thenReturn(mockSubjectList);

        // Mock flash promotion
        List<SmsFlashPromotion> flashPromotions = new ArrayList<>();
        SmsFlashPromotion flashPromotion = new SmsFlashPromotion();
        flashPromotion.setId(1L);
        flashPromotions.add(flashPromotion);
        when(flashPromotionMapper.selectByExample(any(SmsFlashPromotionExample.class)))
                .thenReturn(flashPromotions);

        // Mock flash promotion session
        List<SmsFlashPromotionSession> sessions = new ArrayList<>();
        SmsFlashPromotionSession session = new SmsFlashPromotionSession();
        session.setId(1L);
        session.setStartTime(testDate);
        session.setEndTime(testDate);
        sessions.add(session);
        when(promotionSessionMapper.selectByExample(any(SmsFlashPromotionSessionExample.class)))
                .thenReturn(sessions);

        // Mock flash promotion products
        List<FlashPromotionProduct> flashProducts = new ArrayList<>();
        FlashPromotionProduct flashProduct = new FlashPromotionProduct();
        flashProduct.setId(1L);
        flashProducts.add(flashProduct);
        when(homeDao.getFlashProductList(1L, 1L)).thenReturn(flashProducts);

        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            mockedDateUtil.when(() -> DateUtil.getDate(any(Date.class))).thenReturn(testDate);
            mockedDateUtil.when(() -> DateUtil.getTime(any(Date.class))).thenReturn(testDate);

            // When
            HomeContentResult result = homeService.content();

            // Then
            assertNotNull(result);
            assertNotNull(result.getHomeFlashPromotion());
            assertNotNull(result.getHomeFlashPromotion().getProductList());
            assertEquals(1, result.getHomeFlashPromotion().getProductList().size());
        }
    }

    @Test
    void testRecommendProductList_Success() {
        // Given
        Integer pageSize = 10;
        Integer pageNum = 1;
        when(productMapper.selectByExample(any(PmsProductExample.class)))
                .thenReturn(mockProductList);

        try (MockedStatic<PageHelper> mockedPageHelper = mockStatic(PageHelper.class)) {
            // When
            List<PmsProduct> result = homeService.recommendProductList(pageSize, pageNum);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            
            // 在try块中验证MockedStatic
            mockedPageHelper.verify(() -> PageHelper.startPage(pageNum, pageSize));
        }
        
        // 简化验证，只验证调用次数
        verify(productMapper, times(1)).selectByExample(any(PmsProductExample.class));
    }

    @Test
    void testGetProductCateList_Success() {
        // Given
        Long parentId = 1L;
        List<PmsProductCategory> mockCategories = new ArrayList<>();
        PmsProductCategory category = new PmsProductCategory();
        category.setId(1L);
        category.setName("测试分类");
        mockCategories.add(category);
        
        when(productCategoryMapper.selectByExample(any(PmsProductCategoryExample.class)))
                .thenReturn(mockCategories);

        // When
        List<PmsProductCategory> result = homeService.getProductCateList(parentId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(productCategoryMapper).selectByExample(any(PmsProductCategoryExample.class));
    }

    @Test
    void testGetSubjectList_WithCateId() {
        // Given
        Long cateId = 1L;
        Integer pageSize = 10;
        Integer pageNum = 1;
        when(subjectMapper.selectByExample(any(CmsSubjectExample.class)))
                .thenReturn(mockSubjectList);

        try (MockedStatic<PageHelper> mockedPageHelper = mockStatic(PageHelper.class)) {
            // When
            List<CmsSubject> result = homeService.getSubjectList(cateId, pageSize, pageNum);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            
            // 在try块中验证MockedStatic
            mockedPageHelper.verify(() -> PageHelper.startPage(pageNum, pageSize));
        }
        
        // 简化验证
        verify(subjectMapper, times(1)).selectByExample(any(CmsSubjectExample.class));
    }

    @Test
    void testGetSubjectList_WithoutCateId() {
        // Given
        Long cateId = null;
        Integer pageSize = 10;
        Integer pageNum = 1;
        when(subjectMapper.selectByExample(any(CmsSubjectExample.class)))
                .thenReturn(mockSubjectList);

        try (MockedStatic<PageHelper> mockedPageHelper = mockStatic(PageHelper.class)) {
            // When
            List<CmsSubject> result = homeService.getSubjectList(cateId, pageSize, pageNum);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            
            // 在try块中验证MockedStatic
            mockedPageHelper.verify(() -> PageHelper.startPage(pageNum, pageSize));
        }
        
        // 简化验证
        verify(subjectMapper, times(1)).selectByExample(any(CmsSubjectExample.class));
    }

    @Test
    void testHotProductList_Success() {
        // Given
        Integer pageNum = 2;
        Integer pageSize = 10;
        int expectedOffset = 10; // pageSize * (pageNum - 1) = 10 * (2 - 1) = 10
        
        when(homeDao.getHotProductList(expectedOffset, pageSize)).thenReturn(mockProductList);

        // When
        List<PmsProduct> result = homeService.hotProductList(pageNum, pageSize);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(homeDao).getHotProductList(expectedOffset, pageSize);
    }

    @Test
    void testNewProductList_Success() {
        // Given
        Integer pageNum = 3;
        Integer pageSize = 5;
        int expectedOffset = 10; // pageSize * (pageNum - 1) = 5 * (3 - 1) = 10
        
        when(homeDao.getNewProductList(expectedOffset, pageSize)).thenReturn(mockProductList);

        // When
        List<PmsProduct> result = homeService.newProductList(pageNum, pageSize);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(homeDao).getNewProductList(expectedOffset, pageSize);
    }

    @Test
    void testContent_EmptyLists() {
        // Given - 返回空列表
        when(advertiseMapper.selectByExample(any(SmsHomeAdvertiseExample.class)))
                .thenReturn(new ArrayList<>());
        when(homeDao.getRecommendBrandList(0, 6)).thenReturn(new ArrayList<>());
        when(homeDao.getNewProductList(0, 4)).thenReturn(new ArrayList<>());
        when(homeDao.getHotProductList(0, 4)).thenReturn(new ArrayList<>());
        when(homeDao.getRecommendSubjectList(0, 4)).thenReturn(new ArrayList<>());
        when(flashPromotionMapper.selectByExample(any(SmsFlashPromotionExample.class)))
                .thenReturn(new ArrayList<>());

        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            mockedDateUtil.when(() -> DateUtil.getDate(any(Date.class))).thenReturn(testDate);
            mockedDateUtil.when(() -> DateUtil.getTime(any(Date.class))).thenReturn(testDate);

            // When
            HomeContentResult result = homeService.content();

            // Then
            assertNotNull(result);
            assertNotNull(result.getAdvertiseList());
            assertNotNull(result.getBrandList());
            assertNotNull(result.getNewProductList());
            assertNotNull(result.getHotProductList());
            assertNotNull(result.getSubjectList());
            assertTrue(result.getAdvertiseList().isEmpty());
            assertTrue(result.getBrandList().isEmpty());
            assertTrue(result.getNewProductList().isEmpty());
            assertTrue(result.getHotProductList().isEmpty());
            assertTrue(result.getSubjectList().isEmpty());
        }
    }
}