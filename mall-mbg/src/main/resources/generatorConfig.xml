<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="generator.properties"/>
    <context id="MySqlContext" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 配置SQL语句中的前置分隔符 -->
        <property name="beginningDelimiter" value="`"/>
        <!-- 配置SQL语句中的后置分隔符 -->
        <property name="endingDelimiter" value="`"/>
        <!-- 配置生成Java文件的编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 为模型生成序列化方法 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <!-- 生成mapper.xml时覆盖原文件 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin" />
        <commentGenerator type="com.macro.mall.CommentGenerator">
            <!-- 是否阻止生成的注释 -->
            <property name="suppressAllComments" value="true"/>
            <!-- 是否阻止生成的注释包含时间戳 -->
            <property name="suppressDate" value="true"/>
            <!-- 是否添加数据库表的备注信息 -->
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>
        <!-- 配置MBG要连接的数据库信息 -->
        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.connectionURL}"
                        userId="${jdbc.userId}"
                        password="${jdbc.password}">
            <!-- 解决mysql驱动升级到8.0后不生成指定数据库代码的问题 -->
            <property name="nullCatalogMeansCurrent" value="true" />
        </jdbcConnection>
        <!-- 用于控制实体类的生成 -->
        <javaModelGenerator targetPackage="com.macro.mall.model" targetProject="mall-mbg\src\main\java"/>
        <!-- 用于控制Mapper.xml文件的生成 -->
        <sqlMapGenerator targetPackage="com.macro.mall.mapper" targetProject="mall-mbg\src\main\resources"/>
        <!-- 用于控制Mapper接口的生成 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.macro.mall.mapper"
                             targetProject="mall-mbg\src\main\java"/>
        <!-- 配置需要生成的表，生成全部表tableName设为% -->
        <table tableName="%">
            <!-- 用来指定主键生成策略 -->
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>