<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsFeightTemplateMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsFeightTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="charge_type" jdbcType="INTEGER" property="chargeType" />
    <result column="first_weight" jdbcType="DECIMAL" property="firstWeight" />
    <result column="first_fee" jdbcType="DECIMAL" property="firstFee" />
    <result column="continue_weight" jdbcType="DECIMAL" property="continueWeight" />
    <result column="continme_fee" jdbcType="DECIMAL" property="continmeFee" />
    <result column="dest" jdbcType="VARCHAR" property="dest" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, charge_type, first_weight, first_fee, continue_weight, continme_fee, dest
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsFeightTemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_feight_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pms_feight_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_feight_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsFeightTemplateExample">
    delete from pms_feight_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsFeightTemplate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_feight_template (name, charge_type, first_weight, 
      first_fee, continue_weight, continme_fee, 
      dest)
    values (#{name,jdbcType=VARCHAR}, #{chargeType,jdbcType=INTEGER}, #{firstWeight,jdbcType=DECIMAL}, 
      #{firstFee,jdbcType=DECIMAL}, #{continueWeight,jdbcType=DECIMAL}, #{continmeFee,jdbcType=DECIMAL}, 
      #{dest,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsFeightTemplate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_feight_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="chargeType != null">
        charge_type,
      </if>
      <if test="firstWeight != null">
        first_weight,
      </if>
      <if test="firstFee != null">
        first_fee,
      </if>
      <if test="continueWeight != null">
        continue_weight,
      </if>
      <if test="continmeFee != null">
        continme_fee,
      </if>
      <if test="dest != null">
        dest,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=INTEGER},
      </if>
      <if test="firstWeight != null">
        #{firstWeight,jdbcType=DECIMAL},
      </if>
      <if test="firstFee != null">
        #{firstFee,jdbcType=DECIMAL},
      </if>
      <if test="continueWeight != null">
        #{continueWeight,jdbcType=DECIMAL},
      </if>
      <if test="continmeFee != null">
        #{continmeFee,jdbcType=DECIMAL},
      </if>
      <if test="dest != null">
        #{dest,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsFeightTemplateExample" resultType="java.lang.Long">
    select count(*) from pms_feight_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_feight_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeType != null">
        charge_type = #{record.chargeType,jdbcType=INTEGER},
      </if>
      <if test="record.firstWeight != null">
        first_weight = #{record.firstWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.firstFee != null">
        first_fee = #{record.firstFee,jdbcType=DECIMAL},
      </if>
      <if test="record.continueWeight != null">
        continue_weight = #{record.continueWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.continmeFee != null">
        continme_fee = #{record.continmeFee,jdbcType=DECIMAL},
      </if>
      <if test="record.dest != null">
        dest = #{record.dest,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_feight_template
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      charge_type = #{record.chargeType,jdbcType=INTEGER},
      first_weight = #{record.firstWeight,jdbcType=DECIMAL},
      first_fee = #{record.firstFee,jdbcType=DECIMAL},
      continue_weight = #{record.continueWeight,jdbcType=DECIMAL},
      continme_fee = #{record.continmeFee,jdbcType=DECIMAL},
      dest = #{record.dest,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsFeightTemplate">
    update pms_feight_template
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        charge_type = #{chargeType,jdbcType=INTEGER},
      </if>
      <if test="firstWeight != null">
        first_weight = #{firstWeight,jdbcType=DECIMAL},
      </if>
      <if test="firstFee != null">
        first_fee = #{firstFee,jdbcType=DECIMAL},
      </if>
      <if test="continueWeight != null">
        continue_weight = #{continueWeight,jdbcType=DECIMAL},
      </if>
      <if test="continmeFee != null">
        continme_fee = #{continmeFee,jdbcType=DECIMAL},
      </if>
      <if test="dest != null">
        dest = #{dest,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsFeightTemplate">
    update pms_feight_template
    set name = #{name,jdbcType=VARCHAR},
      charge_type = #{chargeType,jdbcType=INTEGER},
      first_weight = #{firstWeight,jdbcType=DECIMAL},
      first_fee = #{firstFee,jdbcType=DECIMAL},
      continue_weight = #{continueWeight,jdbcType=DECIMAL},
      continme_fee = #{continmeFee,jdbcType=DECIMAL},
      dest = #{dest,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>