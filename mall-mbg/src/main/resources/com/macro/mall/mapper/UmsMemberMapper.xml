<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsMemberMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsMember">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_level_id" jdbcType="BIGINT" property="memberLevelId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="job" jdbcType="VARCHAR" property="job" />
    <result column="personalized_signature" jdbcType="VARCHAR" property="personalizedSignature" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="integration" jdbcType="INTEGER" property="integration" />
    <result column="growth" jdbcType="INTEGER" property="growth" />
    <result column="luckey_count" jdbcType="INTEGER" property="luckeyCount" />
    <result column="history_integration" jdbcType="INTEGER" property="historyIntegration" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_level_id, username, password, nickname, phone, status, create_time, icon, 
    gender, birthday, city, job, personalized_signature, source_type, integration, growth, 
    luckey_count, history_integration
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsMemberExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_member
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_member
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsMemberExample">
    delete from ums_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsMember">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member (member_level_id, username, password, 
      nickname, phone, status, 
      create_time, icon, gender, 
      birthday, city, job, personalized_signature, 
      source_type, integration, growth, 
      luckey_count, history_integration)
    values (#{memberLevelId,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{nickname,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{icon,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, 
      #{birthday,jdbcType=DATE}, #{city,jdbcType=VARCHAR}, #{job,jdbcType=VARCHAR}, #{personalizedSignature,jdbcType=VARCHAR}, 
      #{sourceType,jdbcType=INTEGER}, #{integration,jdbcType=INTEGER}, #{growth,jdbcType=INTEGER}, 
      #{luckeyCount,jdbcType=INTEGER}, #{historyIntegration,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsMember">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberLevelId != null">
        member_level_id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="job != null">
        job,
      </if>
      <if test="personalizedSignature != null">
        personalized_signature,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="integration != null">
        integration,
      </if>
      <if test="growth != null">
        growth,
      </if>
      <if test="luckeyCount != null">
        luckey_count,
      </if>
      <if test="historyIntegration != null">
        history_integration,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberLevelId != null">
        #{memberLevelId,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="job != null">
        #{job,jdbcType=VARCHAR},
      </if>
      <if test="personalizedSignature != null">
        #{personalizedSignature,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="integration != null">
        #{integration,jdbcType=INTEGER},
      </if>
      <if test="growth != null">
        #{growth,jdbcType=INTEGER},
      </if>
      <if test="luckeyCount != null">
        #{luckeyCount,jdbcType=INTEGER},
      </if>
      <if test="historyIntegration != null">
        #{historyIntegration,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsMemberExample" resultType="java.lang.Long">
    select count(*) from ums_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_member
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberLevelId != null">
        member_level_id = #{record.memberLevelId,jdbcType=BIGINT},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.icon != null">
        icon = #{record.icon,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=INTEGER},
      </if>
      <if test="record.birthday != null">
        birthday = #{record.birthday,jdbcType=DATE},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.job != null">
        job = #{record.job,jdbcType=VARCHAR},
      </if>
      <if test="record.personalizedSignature != null">
        personalized_signature = #{record.personalizedSignature,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=INTEGER},
      </if>
      <if test="record.integration != null">
        integration = #{record.integration,jdbcType=INTEGER},
      </if>
      <if test="record.growth != null">
        growth = #{record.growth,jdbcType=INTEGER},
      </if>
      <if test="record.luckeyCount != null">
        luckey_count = #{record.luckeyCount,jdbcType=INTEGER},
      </if>
      <if test="record.historyIntegration != null">
        history_integration = #{record.historyIntegration,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_member
    set id = #{record.id,jdbcType=BIGINT},
      member_level_id = #{record.memberLevelId,jdbcType=BIGINT},
      username = #{record.username,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      icon = #{record.icon,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=INTEGER},
      birthday = #{record.birthday,jdbcType=DATE},
      city = #{record.city,jdbcType=VARCHAR},
      job = #{record.job,jdbcType=VARCHAR},
      personalized_signature = #{record.personalizedSignature,jdbcType=VARCHAR},
      source_type = #{record.sourceType,jdbcType=INTEGER},
      integration = #{record.integration,jdbcType=INTEGER},
      growth = #{record.growth,jdbcType=INTEGER},
      luckey_count = #{record.luckeyCount,jdbcType=INTEGER},
      history_integration = #{record.historyIntegration,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsMember">
    update ums_member
    <set>
      <if test="memberLevelId != null">
        member_level_id = #{memberLevelId,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="job != null">
        job = #{job,jdbcType=VARCHAR},
      </if>
      <if test="personalizedSignature != null">
        personalized_signature = #{personalizedSignature,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="integration != null">
        integration = #{integration,jdbcType=INTEGER},
      </if>
      <if test="growth != null">
        growth = #{growth,jdbcType=INTEGER},
      </if>
      <if test="luckeyCount != null">
        luckey_count = #{luckeyCount,jdbcType=INTEGER},
      </if>
      <if test="historyIntegration != null">
        history_integration = #{historyIntegration,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsMember">
    update ums_member
    set member_level_id = #{memberLevelId,jdbcType=BIGINT},
      username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      icon = #{icon,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      birthday = #{birthday,jdbcType=DATE},
      city = #{city,jdbcType=VARCHAR},
      job = #{job,jdbcType=VARCHAR},
      personalized_signature = #{personalizedSignature,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=INTEGER},
      integration = #{integration,jdbcType=INTEGER},
      growth = #{growth,jdbcType=INTEGER},
      luckey_count = #{luckeyCount,jdbcType=INTEGER},
      history_integration = #{historyIntegration,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>