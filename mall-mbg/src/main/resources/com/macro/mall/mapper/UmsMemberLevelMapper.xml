<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsMemberLevelMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsMemberLevel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="growth_point" jdbcType="INTEGER" property="growthPoint" />
    <result column="default_status" jdbcType="INTEGER" property="defaultStatus" />
    <result column="free_freight_point" jdbcType="DECIMAL" property="freeFreightPoint" />
    <result column="comment_growth_point" jdbcType="INTEGER" property="commentGrowthPoint" />
    <result column="priviledge_free_freight" jdbcType="INTEGER" property="priviledgeFreeFreight" />
    <result column="priviledge_sign_in" jdbcType="INTEGER" property="priviledgeSignIn" />
    <result column="priviledge_comment" jdbcType="INTEGER" property="priviledgeComment" />
    <result column="priviledge_promotion" jdbcType="INTEGER" property="priviledgePromotion" />
    <result column="priviledge_member_price" jdbcType="INTEGER" property="priviledgeMemberPrice" />
    <result column="priviledge_birthday" jdbcType="INTEGER" property="priviledgeBirthday" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, growth_point, default_status, free_freight_point, comment_growth_point, 
    priviledge_free_freight, priviledge_sign_in, priviledge_comment, priviledge_promotion, 
    priviledge_member_price, priviledge_birthday, note
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsMemberLevelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_member_level
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_member_level
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_member_level
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsMemberLevelExample">
    delete from ums_member_level
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsMemberLevel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_level (name, growth_point, default_status, 
      free_freight_point, comment_growth_point, priviledge_free_freight, 
      priviledge_sign_in, priviledge_comment, priviledge_promotion, 
      priviledge_member_price, priviledge_birthday, 
      note)
    values (#{name,jdbcType=VARCHAR}, #{growthPoint,jdbcType=INTEGER}, #{defaultStatus,jdbcType=INTEGER}, 
      #{freeFreightPoint,jdbcType=DECIMAL}, #{commentGrowthPoint,jdbcType=INTEGER}, #{priviledgeFreeFreight,jdbcType=INTEGER}, 
      #{priviledgeSignIn,jdbcType=INTEGER}, #{priviledgeComment,jdbcType=INTEGER}, #{priviledgePromotion,jdbcType=INTEGER}, 
      #{priviledgeMemberPrice,jdbcType=INTEGER}, #{priviledgeBirthday,jdbcType=INTEGER}, 
      #{note,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsMemberLevel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="growthPoint != null">
        growth_point,
      </if>
      <if test="defaultStatus != null">
        default_status,
      </if>
      <if test="freeFreightPoint != null">
        free_freight_point,
      </if>
      <if test="commentGrowthPoint != null">
        comment_growth_point,
      </if>
      <if test="priviledgeFreeFreight != null">
        priviledge_free_freight,
      </if>
      <if test="priviledgeSignIn != null">
        priviledge_sign_in,
      </if>
      <if test="priviledgeComment != null">
        priviledge_comment,
      </if>
      <if test="priviledgePromotion != null">
        priviledge_promotion,
      </if>
      <if test="priviledgeMemberPrice != null">
        priviledge_member_price,
      </if>
      <if test="priviledgeBirthday != null">
        priviledge_birthday,
      </if>
      <if test="note != null">
        note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="growthPoint != null">
        #{growthPoint,jdbcType=INTEGER},
      </if>
      <if test="defaultStatus != null">
        #{defaultStatus,jdbcType=INTEGER},
      </if>
      <if test="freeFreightPoint != null">
        #{freeFreightPoint,jdbcType=DECIMAL},
      </if>
      <if test="commentGrowthPoint != null">
        #{commentGrowthPoint,jdbcType=INTEGER},
      </if>
      <if test="priviledgeFreeFreight != null">
        #{priviledgeFreeFreight,jdbcType=INTEGER},
      </if>
      <if test="priviledgeSignIn != null">
        #{priviledgeSignIn,jdbcType=INTEGER},
      </if>
      <if test="priviledgeComment != null">
        #{priviledgeComment,jdbcType=INTEGER},
      </if>
      <if test="priviledgePromotion != null">
        #{priviledgePromotion,jdbcType=INTEGER},
      </if>
      <if test="priviledgeMemberPrice != null">
        #{priviledgeMemberPrice,jdbcType=INTEGER},
      </if>
      <if test="priviledgeBirthday != null">
        #{priviledgeBirthday,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsMemberLevelExample" resultType="java.lang.Long">
    select count(*) from ums_member_level
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_member_level
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.growthPoint != null">
        growth_point = #{record.growthPoint,jdbcType=INTEGER},
      </if>
      <if test="record.defaultStatus != null">
        default_status = #{record.defaultStatus,jdbcType=INTEGER},
      </if>
      <if test="record.freeFreightPoint != null">
        free_freight_point = #{record.freeFreightPoint,jdbcType=DECIMAL},
      </if>
      <if test="record.commentGrowthPoint != null">
        comment_growth_point = #{record.commentGrowthPoint,jdbcType=INTEGER},
      </if>
      <if test="record.priviledgeFreeFreight != null">
        priviledge_free_freight = #{record.priviledgeFreeFreight,jdbcType=INTEGER},
      </if>
      <if test="record.priviledgeSignIn != null">
        priviledge_sign_in = #{record.priviledgeSignIn,jdbcType=INTEGER},
      </if>
      <if test="record.priviledgeComment != null">
        priviledge_comment = #{record.priviledgeComment,jdbcType=INTEGER},
      </if>
      <if test="record.priviledgePromotion != null">
        priviledge_promotion = #{record.priviledgePromotion,jdbcType=INTEGER},
      </if>
      <if test="record.priviledgeMemberPrice != null">
        priviledge_member_price = #{record.priviledgeMemberPrice,jdbcType=INTEGER},
      </if>
      <if test="record.priviledgeBirthday != null">
        priviledge_birthday = #{record.priviledgeBirthday,jdbcType=INTEGER},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_member_level
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      growth_point = #{record.growthPoint,jdbcType=INTEGER},
      default_status = #{record.defaultStatus,jdbcType=INTEGER},
      free_freight_point = #{record.freeFreightPoint,jdbcType=DECIMAL},
      comment_growth_point = #{record.commentGrowthPoint,jdbcType=INTEGER},
      priviledge_free_freight = #{record.priviledgeFreeFreight,jdbcType=INTEGER},
      priviledge_sign_in = #{record.priviledgeSignIn,jdbcType=INTEGER},
      priviledge_comment = #{record.priviledgeComment,jdbcType=INTEGER},
      priviledge_promotion = #{record.priviledgePromotion,jdbcType=INTEGER},
      priviledge_member_price = #{record.priviledgeMemberPrice,jdbcType=INTEGER},
      priviledge_birthday = #{record.priviledgeBirthday,jdbcType=INTEGER},
      note = #{record.note,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsMemberLevel">
    update ums_member_level
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="growthPoint != null">
        growth_point = #{growthPoint,jdbcType=INTEGER},
      </if>
      <if test="defaultStatus != null">
        default_status = #{defaultStatus,jdbcType=INTEGER},
      </if>
      <if test="freeFreightPoint != null">
        free_freight_point = #{freeFreightPoint,jdbcType=DECIMAL},
      </if>
      <if test="commentGrowthPoint != null">
        comment_growth_point = #{commentGrowthPoint,jdbcType=INTEGER},
      </if>
      <if test="priviledgeFreeFreight != null">
        priviledge_free_freight = #{priviledgeFreeFreight,jdbcType=INTEGER},
      </if>
      <if test="priviledgeSignIn != null">
        priviledge_sign_in = #{priviledgeSignIn,jdbcType=INTEGER},
      </if>
      <if test="priviledgeComment != null">
        priviledge_comment = #{priviledgeComment,jdbcType=INTEGER},
      </if>
      <if test="priviledgePromotion != null">
        priviledge_promotion = #{priviledgePromotion,jdbcType=INTEGER},
      </if>
      <if test="priviledgeMemberPrice != null">
        priviledge_member_price = #{priviledgeMemberPrice,jdbcType=INTEGER},
      </if>
      <if test="priviledgeBirthday != null">
        priviledge_birthday = #{priviledgeBirthday,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsMemberLevel">
    update ums_member_level
    set name = #{name,jdbcType=VARCHAR},
      growth_point = #{growthPoint,jdbcType=INTEGER},
      default_status = #{defaultStatus,jdbcType=INTEGER},
      free_freight_point = #{freeFreightPoint,jdbcType=DECIMAL},
      comment_growth_point = #{commentGrowthPoint,jdbcType=INTEGER},
      priviledge_free_freight = #{priviledgeFreeFreight,jdbcType=INTEGER},
      priviledge_sign_in = #{priviledgeSignIn,jdbcType=INTEGER},
      priviledge_comment = #{priviledgeComment,jdbcType=INTEGER},
      priviledge_promotion = #{priviledgePromotion,jdbcType=INTEGER},
      priviledge_member_price = #{priviledgeMemberPrice,jdbcType=INTEGER},
      priviledge_birthday = #{priviledgeBirthday,jdbcType=INTEGER},
      note = #{note,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>