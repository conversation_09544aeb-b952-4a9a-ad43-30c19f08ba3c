<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsProductOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsProductOperateLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="price_old" jdbcType="DECIMAL" property="priceOld" />
    <result column="price_new" jdbcType="DECIMAL" property="priceNew" />
    <result column="sale_price_old" jdbcType="DECIMAL" property="salePriceOld" />
    <result column="sale_price_new" jdbcType="DECIMAL" property="salePriceNew" />
    <result column="gift_point_old" jdbcType="INTEGER" property="giftPointOld" />
    <result column="gift_point_new" jdbcType="INTEGER" property="giftPointNew" />
    <result column="use_point_limit_old" jdbcType="INTEGER" property="usePointLimitOld" />
    <result column="use_point_limit_new" jdbcType="INTEGER" property="usePointLimitNew" />
    <result column="operate_man" jdbcType="VARCHAR" property="operateMan" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_id, price_old, price_new, sale_price_old, sale_price_new, gift_point_old, 
    gift_point_new, use_point_limit_old, use_point_limit_new, operate_man, create_time
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsProductOperateLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_product_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pms_product_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_product_operate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsProductOperateLogExample">
    delete from pms_product_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsProductOperateLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_product_operate_log (product_id, price_old, price_new, 
      sale_price_old, sale_price_new, gift_point_old, 
      gift_point_new, use_point_limit_old, use_point_limit_new, 
      operate_man, create_time)
    values (#{productId,jdbcType=BIGINT}, #{priceOld,jdbcType=DECIMAL}, #{priceNew,jdbcType=DECIMAL}, 
      #{salePriceOld,jdbcType=DECIMAL}, #{salePriceNew,jdbcType=DECIMAL}, #{giftPointOld,jdbcType=INTEGER}, 
      #{giftPointNew,jdbcType=INTEGER}, #{usePointLimitOld,jdbcType=INTEGER}, #{usePointLimitNew,jdbcType=INTEGER}, 
      #{operateMan,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsProductOperateLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_product_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        product_id,
      </if>
      <if test="priceOld != null">
        price_old,
      </if>
      <if test="priceNew != null">
        price_new,
      </if>
      <if test="salePriceOld != null">
        sale_price_old,
      </if>
      <if test="salePriceNew != null">
        sale_price_new,
      </if>
      <if test="giftPointOld != null">
        gift_point_old,
      </if>
      <if test="giftPointNew != null">
        gift_point_new,
      </if>
      <if test="usePointLimitOld != null">
        use_point_limit_old,
      </if>
      <if test="usePointLimitNew != null">
        use_point_limit_new,
      </if>
      <if test="operateMan != null">
        operate_man,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="priceOld != null">
        #{priceOld,jdbcType=DECIMAL},
      </if>
      <if test="priceNew != null">
        #{priceNew,jdbcType=DECIMAL},
      </if>
      <if test="salePriceOld != null">
        #{salePriceOld,jdbcType=DECIMAL},
      </if>
      <if test="salePriceNew != null">
        #{salePriceNew,jdbcType=DECIMAL},
      </if>
      <if test="giftPointOld != null">
        #{giftPointOld,jdbcType=INTEGER},
      </if>
      <if test="giftPointNew != null">
        #{giftPointNew,jdbcType=INTEGER},
      </if>
      <if test="usePointLimitOld != null">
        #{usePointLimitOld,jdbcType=INTEGER},
      </if>
      <if test="usePointLimitNew != null">
        #{usePointLimitNew,jdbcType=INTEGER},
      </if>
      <if test="operateMan != null">
        #{operateMan,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsProductOperateLogExample" resultType="java.lang.Long">
    select count(*) from pms_product_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_product_operate_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.priceOld != null">
        price_old = #{record.priceOld,jdbcType=DECIMAL},
      </if>
      <if test="record.priceNew != null">
        price_new = #{record.priceNew,jdbcType=DECIMAL},
      </if>
      <if test="record.salePriceOld != null">
        sale_price_old = #{record.salePriceOld,jdbcType=DECIMAL},
      </if>
      <if test="record.salePriceNew != null">
        sale_price_new = #{record.salePriceNew,jdbcType=DECIMAL},
      </if>
      <if test="record.giftPointOld != null">
        gift_point_old = #{record.giftPointOld,jdbcType=INTEGER},
      </if>
      <if test="record.giftPointNew != null">
        gift_point_new = #{record.giftPointNew,jdbcType=INTEGER},
      </if>
      <if test="record.usePointLimitOld != null">
        use_point_limit_old = #{record.usePointLimitOld,jdbcType=INTEGER},
      </if>
      <if test="record.usePointLimitNew != null">
        use_point_limit_new = #{record.usePointLimitNew,jdbcType=INTEGER},
      </if>
      <if test="record.operateMan != null">
        operate_man = #{record.operateMan,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_product_operate_log
    set id = #{record.id,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      price_old = #{record.priceOld,jdbcType=DECIMAL},
      price_new = #{record.priceNew,jdbcType=DECIMAL},
      sale_price_old = #{record.salePriceOld,jdbcType=DECIMAL},
      sale_price_new = #{record.salePriceNew,jdbcType=DECIMAL},
      gift_point_old = #{record.giftPointOld,jdbcType=INTEGER},
      gift_point_new = #{record.giftPointNew,jdbcType=INTEGER},
      use_point_limit_old = #{record.usePointLimitOld,jdbcType=INTEGER},
      use_point_limit_new = #{record.usePointLimitNew,jdbcType=INTEGER},
      operate_man = #{record.operateMan,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsProductOperateLog">
    update pms_product_operate_log
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="priceOld != null">
        price_old = #{priceOld,jdbcType=DECIMAL},
      </if>
      <if test="priceNew != null">
        price_new = #{priceNew,jdbcType=DECIMAL},
      </if>
      <if test="salePriceOld != null">
        sale_price_old = #{salePriceOld,jdbcType=DECIMAL},
      </if>
      <if test="salePriceNew != null">
        sale_price_new = #{salePriceNew,jdbcType=DECIMAL},
      </if>
      <if test="giftPointOld != null">
        gift_point_old = #{giftPointOld,jdbcType=INTEGER},
      </if>
      <if test="giftPointNew != null">
        gift_point_new = #{giftPointNew,jdbcType=INTEGER},
      </if>
      <if test="usePointLimitOld != null">
        use_point_limit_old = #{usePointLimitOld,jdbcType=INTEGER},
      </if>
      <if test="usePointLimitNew != null">
        use_point_limit_new = #{usePointLimitNew,jdbcType=INTEGER},
      </if>
      <if test="operateMan != null">
        operate_man = #{operateMan,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsProductOperateLog">
    update pms_product_operate_log
    set product_id = #{productId,jdbcType=BIGINT},
      price_old = #{priceOld,jdbcType=DECIMAL},
      price_new = #{priceNew,jdbcType=DECIMAL},
      sale_price_old = #{salePriceOld,jdbcType=DECIMAL},
      sale_price_new = #{salePriceNew,jdbcType=DECIMAL},
      gift_point_old = #{giftPointOld,jdbcType=INTEGER},
      gift_point_new = #{giftPointNew,jdbcType=INTEGER},
      use_point_limit_old = #{usePointLimitOld,jdbcType=INTEGER},
      use_point_limit_new = #{usePointLimitNew,jdbcType=INTEGER},
      operate_man = #{operateMan,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>