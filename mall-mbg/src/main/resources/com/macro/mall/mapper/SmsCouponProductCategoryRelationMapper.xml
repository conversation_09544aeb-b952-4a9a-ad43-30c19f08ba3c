<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SmsCouponProductCategoryRelationMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.SmsCouponProductCategoryRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="product_category_id" jdbcType="BIGINT" property="productCategoryId" />
    <result column="product_category_name" jdbcType="VARCHAR" property="productCategoryName" />
    <result column="parent_category_name" jdbcType="VARCHAR" property="parentCategoryName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, coupon_id, product_category_id, product_category_name, parent_category_name
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sms_coupon_product_category_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sms_coupon_product_category_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sms_coupon_product_category_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelationExample">
    delete from sms_coupon_product_category_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_coupon_product_category_relation (coupon_id, product_category_id, product_category_name, 
      parent_category_name)
    values (#{couponId,jdbcType=BIGINT}, #{productCategoryId,jdbcType=BIGINT}, #{productCategoryName,jdbcType=VARCHAR}, 
      #{parentCategoryName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_coupon_product_category_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="productCategoryId != null">
        product_category_id,
      </if>
      <if test="productCategoryName != null">
        product_category_name,
      </if>
      <if test="parentCategoryName != null">
        parent_category_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="productCategoryId != null">
        #{productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="productCategoryName != null">
        #{productCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentCategoryName != null">
        #{parentCategoryName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelationExample" resultType="java.lang.Long">
    select count(*) from sms_coupon_product_category_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sms_coupon_product_category_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=BIGINT},
      </if>
      <if test="record.productCategoryId != null">
        product_category_id = #{record.productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.productCategoryName != null">
        product_category_name = #{record.productCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCategoryName != null">
        parent_category_name = #{record.parentCategoryName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sms_coupon_product_category_relation
    set id = #{record.id,jdbcType=BIGINT},
      coupon_id = #{record.couponId,jdbcType=BIGINT},
      product_category_id = #{record.productCategoryId,jdbcType=BIGINT},
      product_category_name = #{record.productCategoryName,jdbcType=VARCHAR},
      parent_category_name = #{record.parentCategoryName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelation">
    update sms_coupon_product_category_relation
    <set>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="productCategoryId != null">
        product_category_id = #{productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="productCategoryName != null">
        product_category_name = #{productCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentCategoryName != null">
        parent_category_name = #{parentCategoryName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.SmsCouponProductCategoryRelation">
    update sms_coupon_product_category_relation
    set coupon_id = #{couponId,jdbcType=BIGINT},
      product_category_id = #{productCategoryId,jdbcType=BIGINT},
      product_category_name = #{productCategoryName,jdbcType=VARCHAR},
      parent_category_name = #{parentCategoryName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>