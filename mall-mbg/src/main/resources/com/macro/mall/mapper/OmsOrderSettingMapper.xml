<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.OmsOrderSettingMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.OmsOrderSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flash_order_overtime" jdbcType="INTEGER" property="flashOrderOvertime" />
    <result column="normal_order_overtime" jdbcType="INTEGER" property="normalOrderOvertime" />
    <result column="confirm_overtime" jdbcType="INTEGER" property="confirmOvertime" />
    <result column="finish_overtime" jdbcType="INTEGER" property="finishOvertime" />
    <result column="comment_overtime" jdbcType="INTEGER" property="commentOvertime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, flash_order_overtime, normal_order_overtime, confirm_overtime, finish_overtime, 
    comment_overtime
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.OmsOrderSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from oms_order_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from oms_order_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from oms_order_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.OmsOrderSettingExample">
    delete from oms_order_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.OmsOrderSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into oms_order_setting (flash_order_overtime, normal_order_overtime, 
      confirm_overtime, finish_overtime, comment_overtime
      )
    values (#{flashOrderOvertime,jdbcType=INTEGER}, #{normalOrderOvertime,jdbcType=INTEGER}, 
      #{confirmOvertime,jdbcType=INTEGER}, #{finishOvertime,jdbcType=INTEGER}, #{commentOvertime,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.OmsOrderSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into oms_order_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flashOrderOvertime != null">
        flash_order_overtime,
      </if>
      <if test="normalOrderOvertime != null">
        normal_order_overtime,
      </if>
      <if test="confirmOvertime != null">
        confirm_overtime,
      </if>
      <if test="finishOvertime != null">
        finish_overtime,
      </if>
      <if test="commentOvertime != null">
        comment_overtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flashOrderOvertime != null">
        #{flashOrderOvertime,jdbcType=INTEGER},
      </if>
      <if test="normalOrderOvertime != null">
        #{normalOrderOvertime,jdbcType=INTEGER},
      </if>
      <if test="confirmOvertime != null">
        #{confirmOvertime,jdbcType=INTEGER},
      </if>
      <if test="finishOvertime != null">
        #{finishOvertime,jdbcType=INTEGER},
      </if>
      <if test="commentOvertime != null">
        #{commentOvertime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.OmsOrderSettingExample" resultType="java.lang.Long">
    select count(*) from oms_order_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update oms_order_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.flashOrderOvertime != null">
        flash_order_overtime = #{record.flashOrderOvertime,jdbcType=INTEGER},
      </if>
      <if test="record.normalOrderOvertime != null">
        normal_order_overtime = #{record.normalOrderOvertime,jdbcType=INTEGER},
      </if>
      <if test="record.confirmOvertime != null">
        confirm_overtime = #{record.confirmOvertime,jdbcType=INTEGER},
      </if>
      <if test="record.finishOvertime != null">
        finish_overtime = #{record.finishOvertime,jdbcType=INTEGER},
      </if>
      <if test="record.commentOvertime != null">
        comment_overtime = #{record.commentOvertime,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update oms_order_setting
    set id = #{record.id,jdbcType=BIGINT},
      flash_order_overtime = #{record.flashOrderOvertime,jdbcType=INTEGER},
      normal_order_overtime = #{record.normalOrderOvertime,jdbcType=INTEGER},
      confirm_overtime = #{record.confirmOvertime,jdbcType=INTEGER},
      finish_overtime = #{record.finishOvertime,jdbcType=INTEGER},
      comment_overtime = #{record.commentOvertime,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.OmsOrderSetting">
    update oms_order_setting
    <set>
      <if test="flashOrderOvertime != null">
        flash_order_overtime = #{flashOrderOvertime,jdbcType=INTEGER},
      </if>
      <if test="normalOrderOvertime != null">
        normal_order_overtime = #{normalOrderOvertime,jdbcType=INTEGER},
      </if>
      <if test="confirmOvertime != null">
        confirm_overtime = #{confirmOvertime,jdbcType=INTEGER},
      </if>
      <if test="finishOvertime != null">
        finish_overtime = #{finishOvertime,jdbcType=INTEGER},
      </if>
      <if test="commentOvertime != null">
        comment_overtime = #{commentOvertime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.OmsOrderSetting">
    update oms_order_setting
    set flash_order_overtime = #{flashOrderOvertime,jdbcType=INTEGER},
      normal_order_overtime = #{normalOrderOvertime,jdbcType=INTEGER},
      confirm_overtime = #{confirmOvertime,jdbcType=INTEGER},
      finish_overtime = #{finishOvertime,jdbcType=INTEGER},
      comment_overtime = #{commentOvertime,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>