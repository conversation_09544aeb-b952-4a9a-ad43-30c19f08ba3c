<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsSkuStockMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsSkuStock">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="stock" jdbcType="INTEGER" property="stock" />
    <result column="low_stock" jdbcType="INTEGER" property="lowStock" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="sale" jdbcType="INTEGER" property="sale" />
    <result column="promotion_price" jdbcType="DECIMAL" property="promotionPrice" />
    <result column="lock_stock" jdbcType="INTEGER" property="lockStock" />
    <result column="sp_data" jdbcType="VARCHAR" property="spData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_id, sku_code, price, stock, low_stock, pic, sale, promotion_price, lock_stock, 
    sp_data
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsSkuStockExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_sku_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pms_sku_stock
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_sku_stock
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsSkuStockExample">
    delete from pms_sku_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsSkuStock">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_sku_stock (product_id, sku_code, price, 
      stock, low_stock, pic, 
      sale, promotion_price, lock_stock, 
      sp_data)
    values (#{productId,jdbcType=BIGINT}, #{skuCode,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{stock,jdbcType=INTEGER}, #{lowStock,jdbcType=INTEGER}, #{pic,jdbcType=VARCHAR}, 
      #{sale,jdbcType=INTEGER}, #{promotionPrice,jdbcType=DECIMAL}, #{lockStock,jdbcType=INTEGER}, 
      #{spData,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsSkuStock">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_sku_stock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        product_id,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="lowStock != null">
        low_stock,
      </if>
      <if test="pic != null">
        pic,
      </if>
      <if test="sale != null">
        sale,
      </if>
      <if test="promotionPrice != null">
        promotion_price,
      </if>
      <if test="lockStock != null">
        lock_stock,
      </if>
      <if test="spData != null">
        sp_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="lowStock != null">
        #{lowStock,jdbcType=INTEGER},
      </if>
      <if test="pic != null">
        #{pic,jdbcType=VARCHAR},
      </if>
      <if test="sale != null">
        #{sale,jdbcType=INTEGER},
      </if>
      <if test="promotionPrice != null">
        #{promotionPrice,jdbcType=DECIMAL},
      </if>
      <if test="lockStock != null">
        #{lockStock,jdbcType=INTEGER},
      </if>
      <if test="spData != null">
        #{spData,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsSkuStockExample" resultType="java.lang.Long">
    select count(*) from pms_sku_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_sku_stock
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.stock != null">
        stock = #{record.stock,jdbcType=INTEGER},
      </if>
      <if test="record.lowStock != null">
        low_stock = #{record.lowStock,jdbcType=INTEGER},
      </if>
      <if test="record.pic != null">
        pic = #{record.pic,jdbcType=VARCHAR},
      </if>
      <if test="record.sale != null">
        sale = #{record.sale,jdbcType=INTEGER},
      </if>
      <if test="record.promotionPrice != null">
        promotion_price = #{record.promotionPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.lockStock != null">
        lock_stock = #{record.lockStock,jdbcType=INTEGER},
      </if>
      <if test="record.spData != null">
        sp_data = #{record.spData,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_sku_stock
    set id = #{record.id,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      stock = #{record.stock,jdbcType=INTEGER},
      low_stock = #{record.lowStock,jdbcType=INTEGER},
      pic = #{record.pic,jdbcType=VARCHAR},
      sale = #{record.sale,jdbcType=INTEGER},
      promotion_price = #{record.promotionPrice,jdbcType=DECIMAL},
      lock_stock = #{record.lockStock,jdbcType=INTEGER},
      sp_data = #{record.spData,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsSkuStock">
    update pms_sku_stock
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="lowStock != null">
        low_stock = #{lowStock,jdbcType=INTEGER},
      </if>
      <if test="pic != null">
        pic = #{pic,jdbcType=VARCHAR},
      </if>
      <if test="sale != null">
        sale = #{sale,jdbcType=INTEGER},
      </if>
      <if test="promotionPrice != null">
        promotion_price = #{promotionPrice,jdbcType=DECIMAL},
      </if>
      <if test="lockStock != null">
        lock_stock = #{lockStock,jdbcType=INTEGER},
      </if>
      <if test="spData != null">
        sp_data = #{spData,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsSkuStock">
    update pms_sku_stock
    set product_id = #{productId,jdbcType=BIGINT},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      stock = #{stock,jdbcType=INTEGER},
      low_stock = #{lowStock,jdbcType=INTEGER},
      pic = #{pic,jdbcType=VARCHAR},
      sale = #{sale,jdbcType=INTEGER},
      promotion_price = #{promotionPrice,jdbcType=DECIMAL},
      lock_stock = #{lockStock,jdbcType=INTEGER},
      sp_data = #{spData,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>