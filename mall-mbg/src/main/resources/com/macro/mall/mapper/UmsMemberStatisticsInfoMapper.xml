<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsMemberStatisticsInfoMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsMemberStatisticsInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="consume_amount" jdbcType="DECIMAL" property="consumeAmount" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="coupon_count" jdbcType="INTEGER" property="couponCount" />
    <result column="comment_count" jdbcType="INTEGER" property="commentCount" />
    <result column="return_order_count" jdbcType="INTEGER" property="returnOrderCount" />
    <result column="login_count" jdbcType="INTEGER" property="loginCount" />
    <result column="attend_count" jdbcType="INTEGER" property="attendCount" />
    <result column="fans_count" jdbcType="INTEGER" property="fansCount" />
    <result column="collect_product_count" jdbcType="INTEGER" property="collectProductCount" />
    <result column="collect_subject_count" jdbcType="INTEGER" property="collectSubjectCount" />
    <result column="collect_topic_count" jdbcType="INTEGER" property="collectTopicCount" />
    <result column="collect_comment_count" jdbcType="INTEGER" property="collectCommentCount" />
    <result column="invite_friend_count" jdbcType="INTEGER" property="inviteFriendCount" />
    <result column="recent_order_time" jdbcType="TIMESTAMP" property="recentOrderTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_id, consume_amount, order_count, coupon_count, comment_count, return_order_count, 
    login_count, attend_count, fans_count, collect_product_count, collect_subject_count, 
    collect_topic_count, collect_comment_count, invite_friend_count, recent_order_time
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsMemberStatisticsInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_member_statistics_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_member_statistics_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_member_statistics_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsMemberStatisticsInfoExample">
    delete from ums_member_statistics_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsMemberStatisticsInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_statistics_info (member_id, consume_amount, order_count, 
      coupon_count, comment_count, return_order_count, 
      login_count, attend_count, fans_count, 
      collect_product_count, collect_subject_count, 
      collect_topic_count, collect_comment_count, 
      invite_friend_count, recent_order_time)
    values (#{memberId,jdbcType=BIGINT}, #{consumeAmount,jdbcType=DECIMAL}, #{orderCount,jdbcType=INTEGER}, 
      #{couponCount,jdbcType=INTEGER}, #{commentCount,jdbcType=INTEGER}, #{returnOrderCount,jdbcType=INTEGER}, 
      #{loginCount,jdbcType=INTEGER}, #{attendCount,jdbcType=INTEGER}, #{fansCount,jdbcType=INTEGER}, 
      #{collectProductCount,jdbcType=INTEGER}, #{collectSubjectCount,jdbcType=INTEGER}, 
      #{collectTopicCount,jdbcType=INTEGER}, #{collectCommentCount,jdbcType=INTEGER}, 
      #{inviteFriendCount,jdbcType=INTEGER}, #{recentOrderTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsMemberStatisticsInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_statistics_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        member_id,
      </if>
      <if test="consumeAmount != null">
        consume_amount,
      </if>
      <if test="orderCount != null">
        order_count,
      </if>
      <if test="couponCount != null">
        coupon_count,
      </if>
      <if test="commentCount != null">
        comment_count,
      </if>
      <if test="returnOrderCount != null">
        return_order_count,
      </if>
      <if test="loginCount != null">
        login_count,
      </if>
      <if test="attendCount != null">
        attend_count,
      </if>
      <if test="fansCount != null">
        fans_count,
      </if>
      <if test="collectProductCount != null">
        collect_product_count,
      </if>
      <if test="collectSubjectCount != null">
        collect_subject_count,
      </if>
      <if test="collectTopicCount != null">
        collect_topic_count,
      </if>
      <if test="collectCommentCount != null">
        collect_comment_count,
      </if>
      <if test="inviteFriendCount != null">
        invite_friend_count,
      </if>
      <if test="recentOrderTime != null">
        recent_order_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="consumeAmount != null">
        #{consumeAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="couponCount != null">
        #{couponCount,jdbcType=INTEGER},
      </if>
      <if test="commentCount != null">
        #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="returnOrderCount != null">
        #{returnOrderCount,jdbcType=INTEGER},
      </if>
      <if test="loginCount != null">
        #{loginCount,jdbcType=INTEGER},
      </if>
      <if test="attendCount != null">
        #{attendCount,jdbcType=INTEGER},
      </if>
      <if test="fansCount != null">
        #{fansCount,jdbcType=INTEGER},
      </if>
      <if test="collectProductCount != null">
        #{collectProductCount,jdbcType=INTEGER},
      </if>
      <if test="collectSubjectCount != null">
        #{collectSubjectCount,jdbcType=INTEGER},
      </if>
      <if test="collectTopicCount != null">
        #{collectTopicCount,jdbcType=INTEGER},
      </if>
      <if test="collectCommentCount != null">
        #{collectCommentCount,jdbcType=INTEGER},
      </if>
      <if test="inviteFriendCount != null">
        #{inviteFriendCount,jdbcType=INTEGER},
      </if>
      <if test="recentOrderTime != null">
        #{recentOrderTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsMemberStatisticsInfoExample" resultType="java.lang.Long">
    select count(*) from ums_member_statistics_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_member_statistics_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=BIGINT},
      </if>
      <if test="record.consumeAmount != null">
        consume_amount = #{record.consumeAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.orderCount != null">
        order_count = #{record.orderCount,jdbcType=INTEGER},
      </if>
      <if test="record.couponCount != null">
        coupon_count = #{record.couponCount,jdbcType=INTEGER},
      </if>
      <if test="record.commentCount != null">
        comment_count = #{record.commentCount,jdbcType=INTEGER},
      </if>
      <if test="record.returnOrderCount != null">
        return_order_count = #{record.returnOrderCount,jdbcType=INTEGER},
      </if>
      <if test="record.loginCount != null">
        login_count = #{record.loginCount,jdbcType=INTEGER},
      </if>
      <if test="record.attendCount != null">
        attend_count = #{record.attendCount,jdbcType=INTEGER},
      </if>
      <if test="record.fansCount != null">
        fans_count = #{record.fansCount,jdbcType=INTEGER},
      </if>
      <if test="record.collectProductCount != null">
        collect_product_count = #{record.collectProductCount,jdbcType=INTEGER},
      </if>
      <if test="record.collectSubjectCount != null">
        collect_subject_count = #{record.collectSubjectCount,jdbcType=INTEGER},
      </if>
      <if test="record.collectTopicCount != null">
        collect_topic_count = #{record.collectTopicCount,jdbcType=INTEGER},
      </if>
      <if test="record.collectCommentCount != null">
        collect_comment_count = #{record.collectCommentCount,jdbcType=INTEGER},
      </if>
      <if test="record.inviteFriendCount != null">
        invite_friend_count = #{record.inviteFriendCount,jdbcType=INTEGER},
      </if>
      <if test="record.recentOrderTime != null">
        recent_order_time = #{record.recentOrderTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_member_statistics_info
    set id = #{record.id,jdbcType=BIGINT},
      member_id = #{record.memberId,jdbcType=BIGINT},
      consume_amount = #{record.consumeAmount,jdbcType=DECIMAL},
      order_count = #{record.orderCount,jdbcType=INTEGER},
      coupon_count = #{record.couponCount,jdbcType=INTEGER},
      comment_count = #{record.commentCount,jdbcType=INTEGER},
      return_order_count = #{record.returnOrderCount,jdbcType=INTEGER},
      login_count = #{record.loginCount,jdbcType=INTEGER},
      attend_count = #{record.attendCount,jdbcType=INTEGER},
      fans_count = #{record.fansCount,jdbcType=INTEGER},
      collect_product_count = #{record.collectProductCount,jdbcType=INTEGER},
      collect_subject_count = #{record.collectSubjectCount,jdbcType=INTEGER},
      collect_topic_count = #{record.collectTopicCount,jdbcType=INTEGER},
      collect_comment_count = #{record.collectCommentCount,jdbcType=INTEGER},
      invite_friend_count = #{record.inviteFriendCount,jdbcType=INTEGER},
      recent_order_time = #{record.recentOrderTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsMemberStatisticsInfo">
    update ums_member_statistics_info
    <set>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="consumeAmount != null">
        consume_amount = #{consumeAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="couponCount != null">
        coupon_count = #{couponCount,jdbcType=INTEGER},
      </if>
      <if test="commentCount != null">
        comment_count = #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="returnOrderCount != null">
        return_order_count = #{returnOrderCount,jdbcType=INTEGER},
      </if>
      <if test="loginCount != null">
        login_count = #{loginCount,jdbcType=INTEGER},
      </if>
      <if test="attendCount != null">
        attend_count = #{attendCount,jdbcType=INTEGER},
      </if>
      <if test="fansCount != null">
        fans_count = #{fansCount,jdbcType=INTEGER},
      </if>
      <if test="collectProductCount != null">
        collect_product_count = #{collectProductCount,jdbcType=INTEGER},
      </if>
      <if test="collectSubjectCount != null">
        collect_subject_count = #{collectSubjectCount,jdbcType=INTEGER},
      </if>
      <if test="collectTopicCount != null">
        collect_topic_count = #{collectTopicCount,jdbcType=INTEGER},
      </if>
      <if test="collectCommentCount != null">
        collect_comment_count = #{collectCommentCount,jdbcType=INTEGER},
      </if>
      <if test="inviteFriendCount != null">
        invite_friend_count = #{inviteFriendCount,jdbcType=INTEGER},
      </if>
      <if test="recentOrderTime != null">
        recent_order_time = #{recentOrderTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsMemberStatisticsInfo">
    update ums_member_statistics_info
    set member_id = #{memberId,jdbcType=BIGINT},
      consume_amount = #{consumeAmount,jdbcType=DECIMAL},
      order_count = #{orderCount,jdbcType=INTEGER},
      coupon_count = #{couponCount,jdbcType=INTEGER},
      comment_count = #{commentCount,jdbcType=INTEGER},
      return_order_count = #{returnOrderCount,jdbcType=INTEGER},
      login_count = #{loginCount,jdbcType=INTEGER},
      attend_count = #{attendCount,jdbcType=INTEGER},
      fans_count = #{fansCount,jdbcType=INTEGER},
      collect_product_count = #{collectProductCount,jdbcType=INTEGER},
      collect_subject_count = #{collectSubjectCount,jdbcType=INTEGER},
      collect_topic_count = #{collectTopicCount,jdbcType=INTEGER},
      collect_comment_count = #{collectCommentCount,jdbcType=INTEGER},
      invite_friend_count = #{inviteFriendCount,jdbcType=INTEGER},
      recent_order_time = #{recentOrderTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>