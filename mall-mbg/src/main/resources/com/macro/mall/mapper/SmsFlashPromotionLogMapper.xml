<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SmsFlashPromotionLogMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.SmsFlashPromotionLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="member_id" jdbcType="INTEGER" property="memberId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="member_phone" jdbcType="VARCHAR" property="memberPhone" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="subscribe_time" jdbcType="TIMESTAMP" property="subscribeTime" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_id, product_id, member_phone, product_name, subscribe_time, send_time
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.SmsFlashPromotionLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sms_flash_promotion_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sms_flash_promotion_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from sms_flash_promotion_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.SmsFlashPromotionLogExample">
    delete from sms_flash_promotion_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.SmsFlashPromotionLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_flash_promotion_log (member_id, product_id, member_phone, 
      product_name, subscribe_time, send_time
      )
    values (#{memberId,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, #{memberPhone,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{subscribeTime,jdbcType=TIMESTAMP}, #{sendTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.SmsFlashPromotionLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_flash_promotion_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        member_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="memberPhone != null">
        member_phone,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="subscribeTime != null">
        subscribe_time,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        #{memberId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="memberPhone != null">
        #{memberPhone,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="subscribeTime != null">
        #{subscribeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.SmsFlashPromotionLogExample" resultType="java.lang.Long">
    select count(*) from sms_flash_promotion_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sms_flash_promotion_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.memberPhone != null">
        member_phone = #{record.memberPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.subscribeTime != null">
        subscribe_time = #{record.subscribeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sms_flash_promotion_log
    set id = #{record.id,jdbcType=INTEGER},
      member_id = #{record.memberId,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      member_phone = #{record.memberPhone,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      subscribe_time = #{record.subscribeTime,jdbcType=TIMESTAMP},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.SmsFlashPromotionLog">
    update sms_flash_promotion_log
    <set>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="memberPhone != null">
        member_phone = #{memberPhone,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="subscribeTime != null">
        subscribe_time = #{subscribeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.SmsFlashPromotionLog">
    update sms_flash_promotion_log
    set member_id = #{memberId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      member_phone = #{memberPhone,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      subscribe_time = #{subscribeTime,jdbcType=TIMESTAMP},
      send_time = #{sendTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>