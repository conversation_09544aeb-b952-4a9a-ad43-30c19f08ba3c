<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.OmsOrderMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.OmsOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="member_username" jdbcType="VARCHAR" property="memberUsername" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount" />
    <result column="promotion_amount" jdbcType="DECIMAL" property="promotionAmount" />
    <result column="integration_amount" jdbcType="DECIMAL" property="integrationAmount" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="delivery_company" jdbcType="VARCHAR" property="deliveryCompany" />
    <result column="delivery_sn" jdbcType="VARCHAR" property="deliverySn" />
    <result column="auto_confirm_day" jdbcType="INTEGER" property="autoConfirmDay" />
    <result column="integration" jdbcType="INTEGER" property="integration" />
    <result column="growth" jdbcType="INTEGER" property="growth" />
    <result column="promotion_info" jdbcType="VARCHAR" property="promotionInfo" />
    <result column="bill_type" jdbcType="INTEGER" property="billType" />
    <result column="bill_header" jdbcType="VARCHAR" property="billHeader" />
    <result column="bill_content" jdbcType="VARCHAR" property="billContent" />
    <result column="bill_receiver_phone" jdbcType="VARCHAR" property="billReceiverPhone" />
    <result column="bill_receiver_email" jdbcType="VARCHAR" property="billReceiverEmail" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="receiver_post_code" jdbcType="VARCHAR" property="receiverPostCode" />
    <result column="receiver_province" jdbcType="VARCHAR" property="receiverProvince" />
    <result column="receiver_city" jdbcType="VARCHAR" property="receiverCity" />
    <result column="receiver_region" jdbcType="VARCHAR" property="receiverRegion" />
    <result column="receiver_detail_address" jdbcType="VARCHAR" property="receiverDetailAddress" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="use_integration" jdbcType="INTEGER" property="useIntegration" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="comment_time" jdbcType="TIMESTAMP" property="commentTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_id, coupon_id, order_sn, create_time, member_username, total_amount, pay_amount, 
    freight_amount, promotion_amount, integration_amount, coupon_amount, discount_amount, 
    pay_type, source_type, status, order_type, delivery_company, delivery_sn, auto_confirm_day, 
    integration, growth, promotion_info, bill_type, bill_header, bill_content, bill_receiver_phone, 
    bill_receiver_email, receiver_name, receiver_phone, receiver_post_code, receiver_province, 
    receiver_city, receiver_region, receiver_detail_address, note, confirm_status, delete_status, 
    use_integration, payment_time, delivery_time, receive_time, comment_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.OmsOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from oms_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from oms_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from oms_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.OmsOrderExample">
    delete from oms_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.OmsOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into oms_order (member_id, coupon_id, order_sn, 
      create_time, member_username, total_amount, 
      pay_amount, freight_amount, promotion_amount, 
      integration_amount, coupon_amount, discount_amount, 
      pay_type, source_type, status, 
      order_type, delivery_company, delivery_sn, 
      auto_confirm_day, integration, growth, 
      promotion_info, bill_type, bill_header, 
      bill_content, bill_receiver_phone, bill_receiver_email, 
      receiver_name, receiver_phone, receiver_post_code, 
      receiver_province, receiver_city, receiver_region, 
      receiver_detail_address, note, confirm_status, 
      delete_status, use_integration, payment_time, 
      delivery_time, receive_time, comment_time, 
      modify_time)
    values (#{memberId,jdbcType=BIGINT}, #{couponId,jdbcType=BIGINT}, #{orderSn,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{memberUsername,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL}, 
      #{payAmount,jdbcType=DECIMAL}, #{freightAmount,jdbcType=DECIMAL}, #{promotionAmount,jdbcType=DECIMAL}, 
      #{integrationAmount,jdbcType=DECIMAL}, #{couponAmount,jdbcType=DECIMAL}, #{discountAmount,jdbcType=DECIMAL}, 
      #{payType,jdbcType=INTEGER}, #{sourceType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{orderType,jdbcType=INTEGER}, #{deliveryCompany,jdbcType=VARCHAR}, #{deliverySn,jdbcType=VARCHAR}, 
      #{autoConfirmDay,jdbcType=INTEGER}, #{integration,jdbcType=INTEGER}, #{growth,jdbcType=INTEGER}, 
      #{promotionInfo,jdbcType=VARCHAR}, #{billType,jdbcType=INTEGER}, #{billHeader,jdbcType=VARCHAR}, 
      #{billContent,jdbcType=VARCHAR}, #{billReceiverPhone,jdbcType=VARCHAR}, #{billReceiverEmail,jdbcType=VARCHAR}, 
      #{receiverName,jdbcType=VARCHAR}, #{receiverPhone,jdbcType=VARCHAR}, #{receiverPostCode,jdbcType=VARCHAR}, 
      #{receiverProvince,jdbcType=VARCHAR}, #{receiverCity,jdbcType=VARCHAR}, #{receiverRegion,jdbcType=VARCHAR}, 
      #{receiverDetailAddress,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, #{confirmStatus,jdbcType=INTEGER}, 
      #{deleteStatus,jdbcType=INTEGER}, #{useIntegration,jdbcType=INTEGER}, #{paymentTime,jdbcType=TIMESTAMP}, 
      #{deliveryTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP}, #{commentTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.OmsOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into oms_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        member_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="orderSn != null">
        order_sn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="memberUsername != null">
        member_username,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="freightAmount != null">
        freight_amount,
      </if>
      <if test="promotionAmount != null">
        promotion_amount,
      </if>
      <if test="integrationAmount != null">
        integration_amount,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="deliveryCompany != null">
        delivery_company,
      </if>
      <if test="deliverySn != null">
        delivery_sn,
      </if>
      <if test="autoConfirmDay != null">
        auto_confirm_day,
      </if>
      <if test="integration != null">
        integration,
      </if>
      <if test="growth != null">
        growth,
      </if>
      <if test="promotionInfo != null">
        promotion_info,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="billHeader != null">
        bill_header,
      </if>
      <if test="billContent != null">
        bill_content,
      </if>
      <if test="billReceiverPhone != null">
        bill_receiver_phone,
      </if>
      <if test="billReceiverEmail != null">
        bill_receiver_email,
      </if>
      <if test="receiverName != null">
        receiver_name,
      </if>
      <if test="receiverPhone != null">
        receiver_phone,
      </if>
      <if test="receiverPostCode != null">
        receiver_post_code,
      </if>
      <if test="receiverProvince != null">
        receiver_province,
      </if>
      <if test="receiverCity != null">
        receiver_city,
      </if>
      <if test="receiverRegion != null">
        receiver_region,
      </if>
      <if test="receiverDetailAddress != null">
        receiver_detail_address,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="confirmStatus != null">
        confirm_status,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="useIntegration != null">
        use_integration,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="commentTime != null">
        comment_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberUsername != null">
        #{memberUsername,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="promotionAmount != null">
        #{promotionAmount,jdbcType=DECIMAL},
      </if>
      <if test="integrationAmount != null">
        #{integrationAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="deliveryCompany != null">
        #{deliveryCompany,jdbcType=VARCHAR},
      </if>
      <if test="deliverySn != null">
        #{deliverySn,jdbcType=VARCHAR},
      </if>
      <if test="autoConfirmDay != null">
        #{autoConfirmDay,jdbcType=INTEGER},
      </if>
      <if test="integration != null">
        #{integration,jdbcType=INTEGER},
      </if>
      <if test="growth != null">
        #{growth,jdbcType=INTEGER},
      </if>
      <if test="promotionInfo != null">
        #{promotionInfo,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=INTEGER},
      </if>
      <if test="billHeader != null">
        #{billHeader,jdbcType=VARCHAR},
      </if>
      <if test="billContent != null">
        #{billContent,jdbcType=VARCHAR},
      </if>
      <if test="billReceiverPhone != null">
        #{billReceiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="billReceiverEmail != null">
        #{billReceiverEmail,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverPostCode != null">
        #{receiverPostCode,jdbcType=VARCHAR},
      </if>
      <if test="receiverProvince != null">
        #{receiverProvince,jdbcType=VARCHAR},
      </if>
      <if test="receiverCity != null">
        #{receiverCity,jdbcType=VARCHAR},
      </if>
      <if test="receiverRegion != null">
        #{receiverRegion,jdbcType=VARCHAR},
      </if>
      <if test="receiverDetailAddress != null">
        #{receiverDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="useIntegration != null">
        #{useIntegration,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commentTime != null">
        #{commentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.OmsOrderExample" resultType="java.lang.Long">
    select count(*) from oms_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update oms_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=BIGINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSn != null">
        order_sn = #{record.orderSn,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.memberUsername != null">
        member_username = #{record.memberUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.freightAmount != null">
        freight_amount = #{record.freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.promotionAmount != null">
        promotion_amount = #{record.promotionAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.integrationAmount != null">
        integration_amount = #{record.integrationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.discountAmount != null">
        discount_amount = #{record.discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryCompany != null">
        delivery_company = #{record.deliveryCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverySn != null">
        delivery_sn = #{record.deliverySn,jdbcType=VARCHAR},
      </if>
      <if test="record.autoConfirmDay != null">
        auto_confirm_day = #{record.autoConfirmDay,jdbcType=INTEGER},
      </if>
      <if test="record.integration != null">
        integration = #{record.integration,jdbcType=INTEGER},
      </if>
      <if test="record.growth != null">
        growth = #{record.growth,jdbcType=INTEGER},
      </if>
      <if test="record.promotionInfo != null">
        promotion_info = #{record.promotionInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.billType != null">
        bill_type = #{record.billType,jdbcType=INTEGER},
      </if>
      <if test="record.billHeader != null">
        bill_header = #{record.billHeader,jdbcType=VARCHAR},
      </if>
      <if test="record.billContent != null">
        bill_content = #{record.billContent,jdbcType=VARCHAR},
      </if>
      <if test="record.billReceiverPhone != null">
        bill_receiver_phone = #{record.billReceiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.billReceiverEmail != null">
        bill_receiver_email = #{record.billReceiverEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverName != null">
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverPhone != null">
        receiver_phone = #{record.receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverPostCode != null">
        receiver_post_code = #{record.receiverPostCode,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverProvince != null">
        receiver_province = #{record.receiverProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverCity != null">
        receiver_city = #{record.receiverCity,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverRegion != null">
        receiver_region = #{record.receiverRegion,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverDetailAddress != null">
        receiver_detail_address = #{record.receiverDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmStatus != null">
        confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="record.useIntegration != null">
        use_integration = #{record.useIntegration,jdbcType=INTEGER},
      </if>
      <if test="record.paymentTime != null">
        payment_time = #{record.paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryTime != null">
        delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commentTime != null">
        comment_time = #{record.commentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update oms_order
    set id = #{record.id,jdbcType=BIGINT},
      member_id = #{record.memberId,jdbcType=BIGINT},
      coupon_id = #{record.couponId,jdbcType=BIGINT},
      order_sn = #{record.orderSn,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      member_username = #{record.memberUsername,jdbcType=VARCHAR},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      freight_amount = #{record.freightAmount,jdbcType=DECIMAL},
      promotion_amount = #{record.promotionAmount,jdbcType=DECIMAL},
      integration_amount = #{record.integrationAmount,jdbcType=DECIMAL},
      coupon_amount = #{record.couponAmount,jdbcType=DECIMAL},
      discount_amount = #{record.discountAmount,jdbcType=DECIMAL},
      pay_type = #{record.payType,jdbcType=INTEGER},
      source_type = #{record.sourceType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      delivery_company = #{record.deliveryCompany,jdbcType=VARCHAR},
      delivery_sn = #{record.deliverySn,jdbcType=VARCHAR},
      auto_confirm_day = #{record.autoConfirmDay,jdbcType=INTEGER},
      integration = #{record.integration,jdbcType=INTEGER},
      growth = #{record.growth,jdbcType=INTEGER},
      promotion_info = #{record.promotionInfo,jdbcType=VARCHAR},
      bill_type = #{record.billType,jdbcType=INTEGER},
      bill_header = #{record.billHeader,jdbcType=VARCHAR},
      bill_content = #{record.billContent,jdbcType=VARCHAR},
      bill_receiver_phone = #{record.billReceiverPhone,jdbcType=VARCHAR},
      bill_receiver_email = #{record.billReceiverEmail,jdbcType=VARCHAR},
      receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      receiver_phone = #{record.receiverPhone,jdbcType=VARCHAR},
      receiver_post_code = #{record.receiverPostCode,jdbcType=VARCHAR},
      receiver_province = #{record.receiverProvince,jdbcType=VARCHAR},
      receiver_city = #{record.receiverCity,jdbcType=VARCHAR},
      receiver_region = #{record.receiverRegion,jdbcType=VARCHAR},
      receiver_detail_address = #{record.receiverDetailAddress,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=VARCHAR},
      confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      use_integration = #{record.useIntegration,jdbcType=INTEGER},
      payment_time = #{record.paymentTime,jdbcType=TIMESTAMP},
      delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      comment_time = #{record.commentTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.OmsOrder">
    update oms_order
    <set>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        order_sn = #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberUsername != null">
        member_username = #{memberUsername,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="freightAmount != null">
        freight_amount = #{freightAmount,jdbcType=DECIMAL},
      </if>
      <if test="promotionAmount != null">
        promotion_amount = #{promotionAmount,jdbcType=DECIMAL},
      </if>
      <if test="integrationAmount != null">
        integration_amount = #{integrationAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="deliveryCompany != null">
        delivery_company = #{deliveryCompany,jdbcType=VARCHAR},
      </if>
      <if test="deliverySn != null">
        delivery_sn = #{deliverySn,jdbcType=VARCHAR},
      </if>
      <if test="autoConfirmDay != null">
        auto_confirm_day = #{autoConfirmDay,jdbcType=INTEGER},
      </if>
      <if test="integration != null">
        integration = #{integration,jdbcType=INTEGER},
      </if>
      <if test="growth != null">
        growth = #{growth,jdbcType=INTEGER},
      </if>
      <if test="promotionInfo != null">
        promotion_info = #{promotionInfo,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=INTEGER},
      </if>
      <if test="billHeader != null">
        bill_header = #{billHeader,jdbcType=VARCHAR},
      </if>
      <if test="billContent != null">
        bill_content = #{billContent,jdbcType=VARCHAR},
      </if>
      <if test="billReceiverPhone != null">
        bill_receiver_phone = #{billReceiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="billReceiverEmail != null">
        bill_receiver_email = #{billReceiverEmail,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiverPostCode != null">
        receiver_post_code = #{receiverPostCode,jdbcType=VARCHAR},
      </if>
      <if test="receiverProvince != null">
        receiver_province = #{receiverProvince,jdbcType=VARCHAR},
      </if>
      <if test="receiverCity != null">
        receiver_city = #{receiverCity,jdbcType=VARCHAR},
      </if>
      <if test="receiverRegion != null">
        receiver_region = #{receiverRegion,jdbcType=VARCHAR},
      </if>
      <if test="receiverDetailAddress != null">
        receiver_detail_address = #{receiverDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        confirm_status = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="useIntegration != null">
        use_integration = #{useIntegration,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commentTime != null">
        comment_time = #{commentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.OmsOrder">
    update oms_order
    set member_id = #{memberId,jdbcType=BIGINT},
      coupon_id = #{couponId,jdbcType=BIGINT},
      order_sn = #{orderSn,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      member_username = #{memberUsername,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      freight_amount = #{freightAmount,jdbcType=DECIMAL},
      promotion_amount = #{promotionAmount,jdbcType=DECIMAL},
      integration_amount = #{integrationAmount,jdbcType=DECIMAL},
      coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      pay_type = #{payType,jdbcType=INTEGER},
      source_type = #{sourceType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      delivery_company = #{deliveryCompany,jdbcType=VARCHAR},
      delivery_sn = #{deliverySn,jdbcType=VARCHAR},
      auto_confirm_day = #{autoConfirmDay,jdbcType=INTEGER},
      integration = #{integration,jdbcType=INTEGER},
      growth = #{growth,jdbcType=INTEGER},
      promotion_info = #{promotionInfo,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=INTEGER},
      bill_header = #{billHeader,jdbcType=VARCHAR},
      bill_content = #{billContent,jdbcType=VARCHAR},
      bill_receiver_phone = #{billReceiverPhone,jdbcType=VARCHAR},
      bill_receiver_email = #{billReceiverEmail,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
      receiver_post_code = #{receiverPostCode,jdbcType=VARCHAR},
      receiver_province = #{receiverProvince,jdbcType=VARCHAR},
      receiver_city = #{receiverCity,jdbcType=VARCHAR},
      receiver_region = #{receiverRegion,jdbcType=VARCHAR},
      receiver_detail_address = #{receiverDetailAddress,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      confirm_status = #{confirmStatus,jdbcType=INTEGER},
      delete_status = #{deleteStatus,jdbcType=INTEGER},
      use_integration = #{useIntegration,jdbcType=INTEGER},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      comment_time = #{commentTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>