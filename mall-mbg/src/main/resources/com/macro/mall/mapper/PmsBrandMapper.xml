<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsBrandMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsBrand">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="first_letter" jdbcType="VARCHAR" property="firstLetter" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="factory_status" jdbcType="INTEGER" property="factoryStatus" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
    <result column="product_count" jdbcType="INTEGER" property="productCount" />
    <result column="product_comment_count" jdbcType="INTEGER" property="productCommentCount" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="big_pic" jdbcType="VARCHAR" property="bigPic" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.macro.mall.model.PmsBrand">
    <result column="brand_story" jdbcType="LONGVARCHAR" property="brandStory" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, first_letter, sort, factory_status, show_status, product_count, product_comment_count, 
    logo, big_pic
  </sql>
  <sql id="Blob_Column_List">
    brand_story
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.macro.mall.model.PmsBrandExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pms_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsBrandExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pms_brand
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_brand
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsBrandExample">
    delete from pms_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsBrand">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_brand (name, first_letter, sort, 
      factory_status, show_status, product_count, 
      product_comment_count, logo, big_pic, 
      brand_story)
    values (#{name,jdbcType=VARCHAR}, #{firstLetter,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{factoryStatus,jdbcType=INTEGER}, #{showStatus,jdbcType=INTEGER}, #{productCount,jdbcType=INTEGER}, 
      #{productCommentCount,jdbcType=INTEGER}, #{logo,jdbcType=VARCHAR}, #{bigPic,jdbcType=VARCHAR}, 
      #{brandStory,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsBrand">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_brand
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="firstLetter != null">
        first_letter,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="factoryStatus != null">
        factory_status,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="productCount != null">
        product_count,
      </if>
      <if test="productCommentCount != null">
        product_comment_count,
      </if>
      <if test="logo != null">
        logo,
      </if>
      <if test="bigPic != null">
        big_pic,
      </if>
      <if test="brandStory != null">
        brand_story,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="firstLetter != null">
        #{firstLetter,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="factoryStatus != null">
        #{factoryStatus,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="productCount != null">
        #{productCount,jdbcType=INTEGER},
      </if>
      <if test="productCommentCount != null">
        #{productCommentCount,jdbcType=INTEGER},
      </if>
      <if test="logo != null">
        #{logo,jdbcType=VARCHAR},
      </if>
      <if test="bigPic != null">
        #{bigPic,jdbcType=VARCHAR},
      </if>
      <if test="brandStory != null">
        #{brandStory,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsBrandExample" resultType="java.lang.Long">
    select count(*) from pms_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_brand
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.firstLetter != null">
        first_letter = #{record.firstLetter,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.factoryStatus != null">
        factory_status = #{record.factoryStatus,jdbcType=INTEGER},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
      <if test="record.productCount != null">
        product_count = #{record.productCount,jdbcType=INTEGER},
      </if>
      <if test="record.productCommentCount != null">
        product_comment_count = #{record.productCommentCount,jdbcType=INTEGER},
      </if>
      <if test="record.logo != null">
        logo = #{record.logo,jdbcType=VARCHAR},
      </if>
      <if test="record.bigPic != null">
        big_pic = #{record.bigPic,jdbcType=VARCHAR},
      </if>
      <if test="record.brandStory != null">
        brand_story = #{record.brandStory,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update pms_brand
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      first_letter = #{record.firstLetter,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      factory_status = #{record.factoryStatus,jdbcType=INTEGER},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      product_count = #{record.productCount,jdbcType=INTEGER},
      product_comment_count = #{record.productCommentCount,jdbcType=INTEGER},
      logo = #{record.logo,jdbcType=VARCHAR},
      big_pic = #{record.bigPic,jdbcType=VARCHAR},
      brand_story = #{record.brandStory,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_brand
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      first_letter = #{record.firstLetter,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      factory_status = #{record.factoryStatus,jdbcType=INTEGER},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      product_count = #{record.productCount,jdbcType=INTEGER},
      product_comment_count = #{record.productCommentCount,jdbcType=INTEGER},
      logo = #{record.logo,jdbcType=VARCHAR},
      big_pic = #{record.bigPic,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsBrand">
    update pms_brand
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="firstLetter != null">
        first_letter = #{firstLetter,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="factoryStatus != null">
        factory_status = #{factoryStatus,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="productCount != null">
        product_count = #{productCount,jdbcType=INTEGER},
      </if>
      <if test="productCommentCount != null">
        product_comment_count = #{productCommentCount,jdbcType=INTEGER},
      </if>
      <if test="logo != null">
        logo = #{logo,jdbcType=VARCHAR},
      </if>
      <if test="bigPic != null">
        big_pic = #{bigPic,jdbcType=VARCHAR},
      </if>
      <if test="brandStory != null">
        brand_story = #{brandStory,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.macro.mall.model.PmsBrand">
    update pms_brand
    set name = #{name,jdbcType=VARCHAR},
      first_letter = #{firstLetter,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      factory_status = #{factoryStatus,jdbcType=INTEGER},
      show_status = #{showStatus,jdbcType=INTEGER},
      product_count = #{productCount,jdbcType=INTEGER},
      product_comment_count = #{productCommentCount,jdbcType=INTEGER},
      logo = #{logo,jdbcType=VARCHAR},
      big_pic = #{bigPic,jdbcType=VARCHAR},
      brand_story = #{brandStory,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsBrand">
    update pms_brand
    set name = #{name,jdbcType=VARCHAR},
      first_letter = #{firstLetter,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      factory_status = #{factoryStatus,jdbcType=INTEGER},
      show_status = #{showStatus,jdbcType=INTEGER},
      product_count = #{productCount,jdbcType=INTEGER},
      product_comment_count = #{productCommentCount,jdbcType=INTEGER},
      logo = #{logo,jdbcType=VARCHAR},
      big_pic = #{bigPic,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>