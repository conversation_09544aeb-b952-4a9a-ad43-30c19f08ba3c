<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsSubjectMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsSubject">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="product_count" jdbcType="INTEGER" property="productCount" />
    <result column="recommend_status" jdbcType="INTEGER" property="recommendStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="collect_count" jdbcType="INTEGER" property="collectCount" />
    <result column="read_count" jdbcType="INTEGER" property="readCount" />
    <result column="comment_count" jdbcType="INTEGER" property="commentCount" />
    <result column="album_pics" jdbcType="VARCHAR" property="albumPics" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
    <result column="forward_count" jdbcType="INTEGER" property="forwardCount" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.macro.mall.model.CmsSubject">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_id, title, pic, product_count, recommend_status, create_time, collect_count, 
    read_count, comment_count, album_pics, description, show_status, forward_count, category_name
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.macro.mall.model.CmsSubjectExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_subject
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsSubjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_subject
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_subject
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cms_subject
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsSubjectExample">
    delete from cms_subject
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsSubject">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_subject (category_id, title, pic, 
      product_count, recommend_status, create_time, 
      collect_count, read_count, comment_count, 
      album_pics, description, show_status, 
      forward_count, category_name, content
      )
    values (#{categoryId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{pic,jdbcType=VARCHAR}, 
      #{productCount,jdbcType=INTEGER}, #{recommendStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{collectCount,jdbcType=INTEGER}, #{readCount,jdbcType=INTEGER}, #{commentCount,jdbcType=INTEGER}, 
      #{albumPics,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{showStatus,jdbcType=INTEGER}, 
      #{forwardCount,jdbcType=INTEGER}, #{categoryName,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsSubject">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_subject
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="pic != null">
        pic,
      </if>
      <if test="productCount != null">
        product_count,
      </if>
      <if test="recommendStatus != null">
        recommend_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="collectCount != null">
        collect_count,
      </if>
      <if test="readCount != null">
        read_count,
      </if>
      <if test="commentCount != null">
        comment_count,
      </if>
      <if test="albumPics != null">
        album_pics,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="forwardCount != null">
        forward_count,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="pic != null">
        #{pic,jdbcType=VARCHAR},
      </if>
      <if test="productCount != null">
        #{productCount,jdbcType=INTEGER},
      </if>
      <if test="recommendStatus != null">
        #{recommendStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="collectCount != null">
        #{collectCount,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        #{readCount,jdbcType=INTEGER},
      </if>
      <if test="commentCount != null">
        #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="albumPics != null">
        #{albumPics,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="forwardCount != null">
        #{forwardCount,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsSubjectExample" resultType="java.lang.Long">
    select count(*) from cms_subject
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_subject
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.pic != null">
        pic = #{record.pic,jdbcType=VARCHAR},
      </if>
      <if test="record.productCount != null">
        product_count = #{record.productCount,jdbcType=INTEGER},
      </if>
      <if test="record.recommendStatus != null">
        recommend_status = #{record.recommendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.collectCount != null">
        collect_count = #{record.collectCount,jdbcType=INTEGER},
      </if>
      <if test="record.readCount != null">
        read_count = #{record.readCount,jdbcType=INTEGER},
      </if>
      <if test="record.commentCount != null">
        comment_count = #{record.commentCount,jdbcType=INTEGER},
      </if>
      <if test="record.albumPics != null">
        album_pics = #{record.albumPics,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
      <if test="record.forwardCount != null">
        forward_count = #{record.forwardCount,jdbcType=INTEGER},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update cms_subject
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      pic = #{record.pic,jdbcType=VARCHAR},
      product_count = #{record.productCount,jdbcType=INTEGER},
      recommend_status = #{record.recommendStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      collect_count = #{record.collectCount,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      comment_count = #{record.commentCount,jdbcType=INTEGER},
      album_pics = #{record.albumPics,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      forward_count = #{record.forwardCount,jdbcType=INTEGER},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_subject
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      pic = #{record.pic,jdbcType=VARCHAR},
      product_count = #{record.productCount,jdbcType=INTEGER},
      recommend_status = #{record.recommendStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      collect_count = #{record.collectCount,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      comment_count = #{record.commentCount,jdbcType=INTEGER},
      album_pics = #{record.albumPics,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      forward_count = #{record.forwardCount,jdbcType=INTEGER},
      category_name = #{record.categoryName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.CmsSubject">
    update cms_subject
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="pic != null">
        pic = #{pic,jdbcType=VARCHAR},
      </if>
      <if test="productCount != null">
        product_count = #{productCount,jdbcType=INTEGER},
      </if>
      <if test="recommendStatus != null">
        recommend_status = #{recommendStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="collectCount != null">
        collect_count = #{collectCount,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        read_count = #{readCount,jdbcType=INTEGER},
      </if>
      <if test="commentCount != null">
        comment_count = #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="albumPics != null">
        album_pics = #{albumPics,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="forwardCount != null">
        forward_count = #{forwardCount,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.macro.mall.model.CmsSubject">
    update cms_subject
    set category_id = #{categoryId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      pic = #{pic,jdbcType=VARCHAR},
      product_count = #{productCount,jdbcType=INTEGER},
      recommend_status = #{recommendStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      collect_count = #{collectCount,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      comment_count = #{commentCount,jdbcType=INTEGER},
      album_pics = #{albumPics,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      show_status = #{showStatus,jdbcType=INTEGER},
      forward_count = #{forwardCount,jdbcType=INTEGER},
      category_name = #{categoryName,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.CmsSubject">
    update cms_subject
    set category_id = #{categoryId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      pic = #{pic,jdbcType=VARCHAR},
      product_count = #{productCount,jdbcType=INTEGER},
      recommend_status = #{recommendStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      collect_count = #{collectCount,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      comment_count = #{commentCount,jdbcType=INTEGER},
      album_pics = #{albumPics,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      show_status = #{showStatus,jdbcType=INTEGER},
      forward_count = #{forwardCount,jdbcType=INTEGER},
      category_name = #{categoryName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>