<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.OmsOrderReturnApplyMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.OmsOrderReturnApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="company_address_id" jdbcType="BIGINT" property="companyAddressId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="member_username" jdbcType="VARCHAR" property="memberUsername" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="return_name" jdbcType="VARCHAR" property="returnName" />
    <result column="return_phone" jdbcType="VARCHAR" property="returnPhone" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="product_pic" jdbcType="VARCHAR" property="productPic" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_brand" jdbcType="VARCHAR" property="productBrand" />
    <result column="product_attr" jdbcType="VARCHAR" property="productAttr" />
    <result column="product_count" jdbcType="INTEGER" property="productCount" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="product_real_price" jdbcType="DECIMAL" property="productRealPrice" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="proof_pics" jdbcType="VARCHAR" property="proofPics" />
    <result column="handle_note" jdbcType="VARCHAR" property="handleNote" />
    <result column="handle_man" jdbcType="VARCHAR" property="handleMan" />
    <result column="receive_man" jdbcType="VARCHAR" property="receiveMan" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="receive_note" jdbcType="VARCHAR" property="receiveNote" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, company_address_id, product_id, order_sn, create_time, member_username, 
    return_amount, return_name, return_phone, status, handle_time, product_pic, product_name, 
    product_brand, product_attr, product_count, product_price, product_real_price, reason, 
    description, proof_pics, handle_note, handle_man, receive_man, receive_time, receive_note
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.OmsOrderReturnApplyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from oms_order_return_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from oms_order_return_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from oms_order_return_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.OmsOrderReturnApplyExample">
    delete from oms_order_return_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.OmsOrderReturnApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into oms_order_return_apply (order_id, company_address_id, product_id, 
      order_sn, create_time, member_username, 
      return_amount, return_name, return_phone, 
      status, handle_time, product_pic, 
      product_name, product_brand, product_attr, 
      product_count, product_price, product_real_price, 
      reason, description, proof_pics, 
      handle_note, handle_man, receive_man, 
      receive_time, receive_note)
    values (#{orderId,jdbcType=BIGINT}, #{companyAddressId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, 
      #{orderSn,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{memberUsername,jdbcType=VARCHAR}, 
      #{returnAmount,jdbcType=DECIMAL}, #{returnName,jdbcType=VARCHAR}, #{returnPhone,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{handleTime,jdbcType=TIMESTAMP}, #{productPic,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{productBrand,jdbcType=VARCHAR}, #{productAttr,jdbcType=VARCHAR}, 
      #{productCount,jdbcType=INTEGER}, #{productPrice,jdbcType=DECIMAL}, #{productRealPrice,jdbcType=DECIMAL}, 
      #{reason,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{proofPics,jdbcType=VARCHAR}, 
      #{handleNote,jdbcType=VARCHAR}, #{handleMan,jdbcType=VARCHAR}, #{receiveMan,jdbcType=VARCHAR}, 
      #{receiveTime,jdbcType=TIMESTAMP}, #{receiveNote,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.OmsOrderReturnApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into oms_order_return_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="companyAddressId != null">
        company_address_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="orderSn != null">
        order_sn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="memberUsername != null">
        member_username,
      </if>
      <if test="returnAmount != null">
        return_amount,
      </if>
      <if test="returnName != null">
        return_name,
      </if>
      <if test="returnPhone != null">
        return_phone,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="handleTime != null">
        handle_time,
      </if>
      <if test="productPic != null">
        product_pic,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productBrand != null">
        product_brand,
      </if>
      <if test="productAttr != null">
        product_attr,
      </if>
      <if test="productCount != null">
        product_count,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="productRealPrice != null">
        product_real_price,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="proofPics != null">
        proof_pics,
      </if>
      <if test="handleNote != null">
        handle_note,
      </if>
      <if test="handleMan != null">
        handle_man,
      </if>
      <if test="receiveMan != null">
        receive_man,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="receiveNote != null">
        receive_note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="companyAddressId != null">
        #{companyAddressId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberUsername != null">
        #{memberUsername,jdbcType=VARCHAR},
      </if>
      <if test="returnAmount != null">
        #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnName != null">
        #{returnName,jdbcType=VARCHAR},
      </if>
      <if test="returnPhone != null">
        #{returnPhone,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="handleTime != null">
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productPic != null">
        #{productPic,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productBrand != null">
        #{productBrand,jdbcType=VARCHAR},
      </if>
      <if test="productAttr != null">
        #{productAttr,jdbcType=VARCHAR},
      </if>
      <if test="productCount != null">
        #{productCount,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="productRealPrice != null">
        #{productRealPrice,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="proofPics != null">
        #{proofPics,jdbcType=VARCHAR},
      </if>
      <if test="handleNote != null">
        #{handleNote,jdbcType=VARCHAR},
      </if>
      <if test="handleMan != null">
        #{handleMan,jdbcType=VARCHAR},
      </if>
      <if test="receiveMan != null">
        #{receiveMan,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveNote != null">
        #{receiveNote,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.OmsOrderReturnApplyExample" resultType="java.lang.Long">
    select count(*) from oms_order_return_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update oms_order_return_apply
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.companyAddressId != null">
        company_address_id = #{record.companyAddressId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSn != null">
        order_sn = #{record.orderSn,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.memberUsername != null">
        member_username = #{record.memberUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.returnAmount != null">
        return_amount = #{record.returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.returnName != null">
        return_name = #{record.returnName,jdbcType=VARCHAR},
      </if>
      <if test="record.returnPhone != null">
        return_phone = #{record.returnPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.handleTime != null">
        handle_time = #{record.handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productPic != null">
        product_pic = #{record.productPic,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.productBrand != null">
        product_brand = #{record.productBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.productAttr != null">
        product_attr = #{record.productAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.productCount != null">
        product_count = #{record.productCount,jdbcType=INTEGER},
      </if>
      <if test="record.productPrice != null">
        product_price = #{record.productPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.productRealPrice != null">
        product_real_price = #{record.productRealPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.proofPics != null">
        proof_pics = #{record.proofPics,jdbcType=VARCHAR},
      </if>
      <if test="record.handleNote != null">
        handle_note = #{record.handleNote,jdbcType=VARCHAR},
      </if>
      <if test="record.handleMan != null">
        handle_man = #{record.handleMan,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveMan != null">
        receive_man = #{record.receiveMan,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.receiveNote != null">
        receive_note = #{record.receiveNote,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update oms_order_return_apply
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      company_address_id = #{record.companyAddressId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      order_sn = #{record.orderSn,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      member_username = #{record.memberUsername,jdbcType=VARCHAR},
      return_amount = #{record.returnAmount,jdbcType=DECIMAL},
      return_name = #{record.returnName,jdbcType=VARCHAR},
      return_phone = #{record.returnPhone,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      handle_time = #{record.handleTime,jdbcType=TIMESTAMP},
      product_pic = #{record.productPic,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_brand = #{record.productBrand,jdbcType=VARCHAR},
      product_attr = #{record.productAttr,jdbcType=VARCHAR},
      product_count = #{record.productCount,jdbcType=INTEGER},
      product_price = #{record.productPrice,jdbcType=DECIMAL},
      product_real_price = #{record.productRealPrice,jdbcType=DECIMAL},
      reason = #{record.reason,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      proof_pics = #{record.proofPics,jdbcType=VARCHAR},
      handle_note = #{record.handleNote,jdbcType=VARCHAR},
      handle_man = #{record.handleMan,jdbcType=VARCHAR},
      receive_man = #{record.receiveMan,jdbcType=VARCHAR},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      receive_note = #{record.receiveNote,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.OmsOrderReturnApply">
    update oms_order_return_apply
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="companyAddressId != null">
        company_address_id = #{companyAddressId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        order_sn = #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberUsername != null">
        member_username = #{memberUsername,jdbcType=VARCHAR},
      </if>
      <if test="returnAmount != null">
        return_amount = #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnName != null">
        return_name = #{returnName,jdbcType=VARCHAR},
      </if>
      <if test="returnPhone != null">
        return_phone = #{returnPhone,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="handleTime != null">
        handle_time = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productPic != null">
        product_pic = #{productPic,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productBrand != null">
        product_brand = #{productBrand,jdbcType=VARCHAR},
      </if>
      <if test="productAttr != null">
        product_attr = #{productAttr,jdbcType=VARCHAR},
      </if>
      <if test="productCount != null">
        product_count = #{productCount,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        product_price = #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="productRealPrice != null">
        product_real_price = #{productRealPrice,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="proofPics != null">
        proof_pics = #{proofPics,jdbcType=VARCHAR},
      </if>
      <if test="handleNote != null">
        handle_note = #{handleNote,jdbcType=VARCHAR},
      </if>
      <if test="handleMan != null">
        handle_man = #{handleMan,jdbcType=VARCHAR},
      </if>
      <if test="receiveMan != null">
        receive_man = #{receiveMan,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveNote != null">
        receive_note = #{receiveNote,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.OmsOrderReturnApply">
    update oms_order_return_apply
    set order_id = #{orderId,jdbcType=BIGINT},
      company_address_id = #{companyAddressId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      order_sn = #{orderSn,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      member_username = #{memberUsername,jdbcType=VARCHAR},
      return_amount = #{returnAmount,jdbcType=DECIMAL},
      return_name = #{returnName,jdbcType=VARCHAR},
      return_phone = #{returnPhone,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      handle_time = #{handleTime,jdbcType=TIMESTAMP},
      product_pic = #{productPic,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_brand = #{productBrand,jdbcType=VARCHAR},
      product_attr = #{productAttr,jdbcType=VARCHAR},
      product_count = #{productCount,jdbcType=INTEGER},
      product_price = #{productPrice,jdbcType=DECIMAL},
      product_real_price = #{productRealPrice,jdbcType=DECIMAL},
      reason = #{reason,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      proof_pics = #{proofPics,jdbcType=VARCHAR},
      handle_note = #{handleNote,jdbcType=VARCHAR},
      handle_man = #{handleMan,jdbcType=VARCHAR},
      receive_man = #{receiveMan,jdbcType=VARCHAR},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      receive_note = #{receiveNote,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>