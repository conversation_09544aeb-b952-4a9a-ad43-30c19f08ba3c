# 完整的上下文信息

## Supervisor 信息
```
<supervisor>
The user's workspace is opened at `/Users/<USER>/Documents/code-test/mall`.
When the user mentions a path, it is probably relative to the workspace directory.
The user's workspace is part of a repository that is currently rooted at `/Users/<USER>/Documents/code-test/mall`.
Use the repository root directory to resolve relative paths supplied to the following tools: codebase-retrieval, save-file, str-replace-editor.
The repository root directory will be the current working directory when launching processes using the `launch-process` tool with `wait=false`.
The interactive terminal's current working directory is `/Users/<USER>/Documents/code-test/mall`.
This is the current working directory used when launching processes using the `launch-process` tool with `wait=true`.

This information may or may not be relevant to the user's current request.
Don't repeat this information to the user.
</supervisor>
```

## 当前打开文件信息
```
The user has the file `pom.xml` open. The user message may or may not be related to this file.
```

## 说明
- 工作空间路径: `/Users/<USER>/Documents/code-test/mall`
- 仓库根目录: `/Users/<USER>/Documents/code-test/mall`
- 当前打开的文件: `pom.xml`
- 终端当前工作目录: `/Users/<USER>/Documents/code-test/mall`

这些信息是系统自动提供给AI助手的上下文信息，用于帮助AI理解当前的工作环境和状态。
