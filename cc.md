# Supervisor Information

The `<supervisor>` section contains dynamic context information that is provided at the beginning of each conversation. Here is the complete supervisor information from the current session:

```
<supervisor>
The user's workspace is opened at `/Users/<USER>/Documents/code-test/mall`.
When the user mentions a path, it is probably relative to the workspace directory.
The user's workspace is part of a repository that is currently rooted at `/Users/<USER>/Documents/code-test/mall`.
Use the repository root directory to resolve relative paths supplied to the following tools: codebase-retrieval, save-file, str-replace-editor.
The repository root directory will be the current working directory when launching processes using the `launch-process` tool with `wait=false`.
The interactive terminal's current working directory is `/Users/<USER>/Documents/code-test/mall`.
This is the current working directory used when launching processes using the `launch-process` tool with `wait=true`.

This information may or may not be relevant to the user's current request.
Don't repeat this information to the user.
</supervisor>
```

## Explanation

The supervisor section provides:

1. **Workspace Location**: The user's current workspace is at `/Users/<USER>/Documents/code-test/mall`
2. **Path Resolution**: When users mention paths, they are relative to the workspace directory
3. **Repository Information**: The workspace is part of a Git repository rooted at the same location
4. **Tool Context**: Instructions for how various tools should resolve paths and set working directories
5. **Process Execution Context**: Different working directories for different types of process launches
6. **Communication Guidelines**: Instructions not to repeat this information to the user

This supervisor information is dynamically injected into each conversation to provide environmental context that helps me understand the user's working environment and properly execute file operations and process launches.
